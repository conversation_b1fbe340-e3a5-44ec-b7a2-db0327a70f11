/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"bac346506d2c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYmFjMzQ2NTA2ZDJjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./contexts/auth-context.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: false,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                            position: \"top-right\",\n                            richColors: true,\n                            closeButton: true,\n                            duration: 4000\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nconst metadata = {\n    generator: 'v0.dev'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjY2FpXFxEZXNrdG9wXFxEZXZcXENlbGxGb3JnZSBBSVxcZnJvbnRlbmRcXGFwcFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\contexts\\auth-context.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Dev\\CellForge AI\\frontend\\contexts\\auth-context.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\layout.tsx\"],\n'loading': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\loading.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(rsc)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFnSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcY2NhaVxcXFxEZXNrdG9wXFxcXERldlxcXFxDZWxsRm9yZ2UgQUlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDY2NhaSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXYlNUMlNUNDZWxsRm9yZ2UlMjBBSSU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNjY2FpJTVDJTVDRGVza3RvcCU1QyU1Q0RldiU1QyU1Q0NlbGxGb3JnZSUyMEFJJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDY2NhaSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXYlNUMlNUNDZWxsRm9yZ2UlMjBBSSU1QyU1Q2Zyb250ZW5kJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQXdKO0FBQ3hKO0FBQ0EsME9BQTJKO0FBQzNKO0FBQ0EsME9BQTJKO0FBQzNKO0FBQ0Esb1JBQWlMO0FBQ2pMO0FBQ0Esd09BQTBKO0FBQzFKO0FBQ0EsNFBBQXFLO0FBQ3JLO0FBQ0Esa1FBQXdLO0FBQ3hLO0FBQ0Esc1FBQXlLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxjY2FpXFxcXERlc2t0b3BcXFxcRGV2XFxcXENlbGxGb3JnZSBBSVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxjY2FpXFxcXERlc2t0b3BcXFxcRGV2XFxcXENlbGxGb3JnZSBBSVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxjY2FpXFxcXERlc2t0b3BcXFxcRGV2XFxcXENlbGxGb3JnZSBBSVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxjY2FpXFxcXERlc2t0b3BcXFxcRGV2XFxcXENlbGxGb3JnZSBBSVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxjY2FpXFxcXERlc2t0b3BcXFxcRGV2XFxcXENlbGxGb3JnZSBBSVxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGNjYWlcXFxcRGVza3RvcFxcXFxEZXZcXFxcQ2VsbEZvcmdlIEFJXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGNjYWlcXFxcRGVza3RvcFxcXFxEZXZcXFxcQ2VsbEZvcmdlIEFJXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGNjYWlcXFxcRGVza3RvcFxcXFxEZXZcXFxcQ2VsbEZvcmdlIEFJXFxcXGZyb250ZW5kXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/form */ \"(ssr)/./components/ui/form.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n// 表单验证schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"请输入邮箱地址\").email(\"请输入有效的邮箱地址\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, \"请输入密码\").min(6, \"密码至少6位字符\")\n});\nfunction LoginPage() {\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { login, isLoading, user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 如果用户已经登录，重定向到主页\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (user) {\n                console.log(\"用户已登录，重定向到主页\");\n                router.push(\"/\");\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        user,\n        router\n    ]);\n    // 初始化表单\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(loginSchema),\n        defaultValues: {\n            email: \"\",\n            password: \"\"\n        }\n    });\n    // 处理表单提交\n    const onSubmit = async (values)=>{\n        setError(\"\");\n        console.log(\"开始登录流程:\", values.email);\n        try {\n            console.log(\"调用登录API...\");\n            await login(values.email, values.password);\n            console.log(\"登录API调用成功\");\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.success(\"登录成功！正在跳转...\");\n            // 延迟跳转，让用户看到成功消息，并确保认证状态已更新\n            console.log(\"准备跳转到主页...\");\n            setTimeout(()=>{\n                console.log(\"执行页面跳转\");\n                try {\n                    router.push(\"/\");\n                    console.log(\"router.push 调用完成\");\n                } catch (routerError) {\n                    console.error(\"路由跳转失败:\", routerError);\n                    // 如果路由跳转失败，尝试使用 window.location\n                    window.location.href = \"/\";\n                }\n            }, 1500);\n        } catch (err) {\n            console.error(\"登录错误:\", err);\n            // 根据错误类型显示不同的错误信息\n            let errorMessage = \"登录失败，请稍后重试\";\n            if (err?.message) {\n                if (err.message.includes(\"邮箱或密码错误\")) {\n                    errorMessage = \"邮箱或密码错误，请检查后重试\";\n                } else if (err.message.includes(\"账户已被暂停\")) {\n                    errorMessage = \"您的账户已被暂停，请联系管理员\";\n                } else if (err.message.includes(\"账户不存在\")) {\n                    errorMessage = \"账户不存在，请检查邮箱地址\";\n                } else if (err.message.includes(\"网络\")) {\n                    errorMessage = \"网络连接失败，请检查网络后重试\";\n                } else {\n                    errorMessage = err.message;\n                }\n            }\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(errorMessage);\n        }\n    };\n    // 切换密码显示状态\n    const togglePasswordVisibility = ()=>{\n        setShowPassword(!showPassword);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                    className: \"space-y-1 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"CF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            className: \"text-2xl font-bold\",\n                            children: \"CellForge AI\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                            children: \"单细胞测序方案咨询系统\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                            variant: \"destructive\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.Form, {\n                            ...form,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: form.handleSubmit(onSubmit),\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormField, {\n                                        control: form.control,\n                                        name: \"email\",\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormLabel, {\n                                                        children: \"邮箱地址\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            type: \"email\",\n                                                            placeholder: \"请输入您的邮箱地址\",\n                                                            ...field,\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormField, {\n                                        control: form.control,\n                                        name: \"password\",\n                                        render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormLabel, {\n                                                                children: \"密码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 23\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"#\",\n                                                                className: \"text-sm text-blue-600 hover:underline\",\n                                                                onClick: (e)=>{\n                                                                    e.preventDefault();\n                                                                    sonner__WEBPACK_IMPORTED_MODULE_11__.toast.info(\"密码重置功能即将上线\");\n                                                                },\n                                                                children: \"忘记密码?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 23\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    placeholder: \"请输入您的密码\",\n                                                                    ...field,\n                                                                    disabled: isLoading,\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: togglePasswordVisibility,\n                                                                    disabled: isLoading,\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 29\n                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 29\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_8__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"登录中...\"\n                                            ]\n                                        }, void 0, true) : \"登录\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardFooter, {\n                    className: \"text-center text-sm text-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"测试账户:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-slate-100 px-1 py-0.5 rounded\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" (超级管理员)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-slate-100 px-1 py-0.5 rounded\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" (销售人员)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-slate-100 px-1 py-0.5 rounded\",\n                                        children: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" (客户)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1\",\n                                children: \"密码分别为: Admin123456, Sales123456, Test123456\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx0aGVtZS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHtcbiAgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIsXG4gIHR5cGUgVGhlbWVQcm92aWRlclByb3BzLFxufSBmcm9tICduZXh0LXRoZW1lcydcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/form.tsx":
/*!********************************!*\
  !*** ./components/ui/form.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormControl: () => (/* binding */ FormControl),\n/* harmony export */   FormDescription: () => (/* binding */ FormDescription),\n/* harmony export */   FormField: () => (/* binding */ FormField),\n/* harmony export */   FormItem: () => (/* binding */ FormItem),\n/* harmony export */   FormLabel: () => (/* binding */ FormLabel),\n/* harmony export */   FormMessage: () => (/* binding */ FormMessage),\n/* harmony export */   useFormField: () => (/* binding */ useFormField)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ useFormField,Form,FormItem,FormLabel,FormControl,FormDescription,FormMessage,FormField auto */ \n\n\n\n\n\nconst Form = react_hook_form__WEBPACK_IMPORTED_MODULE_4__.FormProvider;\nconst FormFieldContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nconst FormField = ({ ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormFieldContext.Provider, {\n        value: {\n            name: props.name\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_4__.Controller, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\nconst useFormField = ()=>{\n    const fieldContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(FormFieldContext);\n    const itemContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(FormItemContext);\n    const { getFieldState, formState } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__.useFormContext)();\n    const fieldState = getFieldState(fieldContext.name, formState);\n    if (!fieldContext) {\n        throw new Error(\"useFormField should be used within <FormField>\");\n    }\n    const { id } = itemContext;\n    return {\n        id,\n        name: fieldContext.name,\n        formItemId: `${id}-form-item`,\n        formDescriptionId: `${id}-form-item-description`,\n        formMessageId: `${id}-form-item-message`,\n        ...fieldState\n    };\n};\nconst FormItemContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext({});\nconst FormItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    const id = react__WEBPACK_IMPORTED_MODULE_1__.useId();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormItemContext.Provider, {\n        value: {\n            id\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n});\nFormItem.displayName = \"FormItem\";\nconst FormLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    const { error, formItemId } = useFormField();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(error && \"text-destructive\", className),\n        htmlFor: formItemId,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, undefined);\n});\nFormLabel.displayName = \"FormLabel\";\nconst FormControl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ ...props }, ref)=>{\n    const { error, formItemId, formDescriptionId, formMessageId } = useFormField();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_5__.Slot, {\n        ref: ref,\n        id: formItemId,\n        \"aria-describedby\": !error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`,\n        \"aria-invalid\": !!error,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n});\nFormControl.displayName = \"FormControl\";\nconst FormDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    const { formDescriptionId } = useFormField();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        id: formDescriptionId,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, undefined);\n});\nFormDescription.displayName = \"FormDescription\";\nconst FormMessage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>{\n    const { error, formMessageId } = useFormField();\n    const body = error ? String(error?.message) : children;\n    if (!body) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        id: formMessageId,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-medium text-destructive\", className),\n        ...props,\n        children: body\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\form.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n});\nFormMessage.displayName = \"FormMessage\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/form.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNjYWlcXERlc2t0b3BcXERldlxcQ2VsbEZvcmdlIEFJXFxmcm9udGVuZFxcY29tcG9uZW50c1xcdWlcXGlucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBSZWFjdC5Db21wb25lbnRQcm9wczxcImlucHV0XCI+PihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LWJhc2UgcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gZmlsZTp0ZXh0LWZvcmVncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst defaultPermissions = [\n    {\n        id: \"view_dashboard\",\n        name: \"查看仪表盘\",\n        description: \"允许用户查看数据分析仪表盘\",\n        roles: [\n            \"super_admin\",\n            \"sales\"\n        ]\n    },\n    {\n        id: \"manage_customers\",\n        name: \"管理客户\",\n        description: \"允许用户添加、编辑和删除客户信息\",\n        roles: [\n            \"super_admin\",\n            \"sales\"\n        ]\n    },\n    {\n        id: \"view_customers\",\n        name: \"查看客户\",\n        description: \"允许用户查看客户信息\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"manage_knowledge\",\n        name: \"管理知识库\",\n        description: \"允许用户添加、编辑和删除知识库内容\",\n        roles: [\n            \"super_admin\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"view_knowledge\",\n        name: \"查看知识库\",\n        description: \"允许用户查看知识库内容\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\",\n            \"customer\"\n        ]\n    },\n    {\n        id: \"manage_solutions\",\n        name: \"管理解决方案\",\n        description: \"允许用户创建和编辑解决方案\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"view_solutions\",\n        name: \"查看解决方案\",\n        description: \"允许用户查看解决方案\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\",\n            \"customer\"\n        ]\n    },\n    {\n        id: \"manage_users\",\n        name: \"管理用户\",\n        description: \"允许用户添加、编辑和删除用户账户\",\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    {\n        id: \"manage_permissions\",\n        name: \"管理权限\",\n        description: \"允许用户配置系统权限\",\n        roles: [\n            \"super_admin\"\n        ]\n    }\n];\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 辅助函数：将API用户转换为前端用户格式\nfunction transformApiUser(apiUser) {\n    // 构建用户显示名称\n    let displayName = apiUser.username; // 默认使用用户名\n    if (apiUser.first_name && apiUser.last_name) {\n        displayName = `${apiUser.first_name} ${apiUser.last_name}`;\n    } else if (apiUser.first_name) {\n        displayName = apiUser.first_name;\n    } else if (apiUser.last_name) {\n        displayName = apiUser.last_name;\n    }\n    return {\n        ...apiUser,\n        id: apiUser.id.toString(),\n        name: displayName,\n        avatar: \"/placeholder.svg?height=40&width=40&query=avatar\"\n    };\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultPermissions);\n    // 从API加载用户信息\n    const loadUser = async ()=>{\n        try {\n            // 检查是否有有效的token\n            const token = localStorage.getItem('cellforge_access_token');\n            if (!token) {\n                setUser(null);\n                return;\n            }\n            const apiUser = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getCurrentUser();\n            const transformedUser = transformApiUser(apiUser);\n            setUser(transformedUser);\n        } catch (error) {\n            console.error(\"Failed to load user:\", error);\n            // 如果获取用户失败，清除可能过期的token\n            _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // 只在客户端执行\n                        if (false) {} else {\n                            // 服务端渲染时设置为null\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error(\"Auth initialization failed:\", error);\n                        setUser(null);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const hasPermission = (permission)=>{\n        return (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(permission, user?.role);\n    };\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        try {\n            // 调用登录API\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                email,\n                password\n            });\n            // 登录成功后获取用户信息\n            await loadUser();\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.register(userData);\n        // 注册成功，但不自动登录，需要等待管理员审核\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshUser = async ()=>{\n        await loadUser();\n    };\n    const logout = ()=>{\n        _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            permissions,\n            hasPermission,\n            login,\n            register,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   conversationApi: () => (/* binding */ conversationApi),\n/* harmony export */   customerProfileApi: () => (/* binding */ customerProfileApi),\n/* harmony export */   intelligentRecommendationApi: () => (/* binding */ intelligentRecommendationApi),\n/* harmony export */   literatureApi: () => (/* binding */ literatureApi),\n/* harmony export */   smartLiteratureApi: () => (/* binding */ smartLiteratureApi),\n/* harmony export */   solutionApi: () => (/* binding */ solutionApi)\n/* harmony export */ });\n/**\n * API调用工具函数\n */ // 动态获取API基础URL\nconst getApiBaseUrl = ()=>{\n    // 优先使用环境变量\n    if (process.env.NEXT_PUBLIC_API_URL) {\n        return process.env.NEXT_PUBLIC_API_URL;\n    }\n    // 在浏览器环境中，使用当前主机的IP和端口8000\n    if (false) {}\n    // 服务端渲染时的默认值\n    return 'http://localhost:8000/api/v1';\n};\nconst API_BASE_URL = getApiBaseUrl();\n// Token管理\nclass TokenManager {\n    static{\n        this.TOKEN_KEY = 'cellforge_access_token';\n    }\n    static{\n        this.REFRESH_KEY = 'cellforge_refresh_token';\n    }\n    static getToken() {\n        if (true) return null;\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static setToken(token) {\n        if (true) return;\n        localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    static removeToken() {\n        if (true) return;\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_KEY);\n    }\n    static isTokenExpired(token) {\n        try {\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.exp * 1000 < Date.now();\n        } catch  {\n            return true;\n        }\n    }\n}\n// HTTP客户端类\nclass ApiClient {\n    constructor(baseURL = API_BASE_URL){\n        this.baseURL = baseURL;\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseURL}${endpoint}`;\n        const token = TokenManager.getToken();\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        // 添加认证头\n        if (token && !TokenManager.isTokenExpired(token)) {\n            headers.Authorization = `Bearer ${token}`;\n        }\n        const config = {\n            ...options,\n            headers\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                // 根据HTTP状态码提供更详细的错误信息\n                let errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`;\n                if (response.status === 401) {\n                    errorMessage = errorData.detail || \"邮箱或密码错误\";\n                } else if (response.status === 403) {\n                    errorMessage = errorData.detail || \"账户已被暂停或无权限访问\";\n                } else if (response.status === 404) {\n                    errorMessage = \"请求的资源不存在\";\n                } else if (response.status === 422) {\n                    errorMessage = \"请求数据格式错误\";\n                } else if (response.status >= 500) {\n                    errorMessage = \"服务器内部错误，请稍后重试\";\n                }\n                throw new Error(errorMessage);\n            }\n            return await response.json();\n        } catch (error) {\n            // 网络错误处理\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                throw new Error('网络连接失败，请检查网络连接');\n            }\n            console.error('API请求失败:', error);\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n}\n// 创建API客户端实例\nconst apiClient = new ApiClient();\n// 认证API\nconst authApi = {\n    // 用户登录\n    async login (credentials) {\n        const response = await apiClient.post('/auth/login', credentials);\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 用户注册\n    async register (userData) {\n        return apiClient.post('/auth/register', userData);\n    },\n    // 获取当前用户信息\n    async getCurrentUser () {\n        return apiClient.get('/auth/me');\n    },\n    // 刷新令牌\n    async refreshToken () {\n        const response = await apiClient.post('/auth/refresh');\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 修改密码\n    async changePassword (data) {\n        return apiClient.post('/auth/change-password', data);\n    },\n    // 登出\n    logout () {\n        TokenManager.removeToken();\n    }\n};\n// 对话API\nconst conversationApi = {\n    // 发送消息\n    async sendMessage (data) {\n        return apiClient.post('/conversation/chat', data);\n    },\n    // 获取对话历史\n    async getHistory (page = 1, size = 20) {\n        return apiClient.get(`/conversation/history?page=${page}&size=${size}`);\n    }\n};\n// 文献API\nconst literatureApi = {\n    // 搜索文献\n    async searchLiterature (data) {\n        return apiClient.post('/literature/search', data);\n    },\n    // 获取文献推荐\n    async getLiteratureRecommendations (data) {\n        return apiClient.post('/literature/recommendations', data);\n    },\n    // 获取文献分类\n    async getCategories () {\n        return apiClient.get('/literature/categories');\n    },\n    // 获取热门文献\n    async getTrendingLiterature (limit = 10) {\n        return apiClient.get(`/literature/trending?limit=${limit}`);\n    },\n    // 获取文献统计\n    async getStats () {\n        return apiClient.get('/literature/stats');\n    },\n    // 获取文献搜索状态\n    async getSearchStatus () {\n        return apiClient.get('/literature/search-status');\n    },\n    // 基于需求搜集文献\n    async collectForRequirements (data) {\n        return apiClient.post('/literature/collect-for-requirements', data);\n    }\n};\n// 客户画像API\nconst customerProfileApi = {\n    // 获取客户画像\n    async getProfile (userId) {\n        return apiClient.get(`/customer/profile/${userId}`);\n    },\n    // 分析客户画像\n    async analyzeProfile (data) {\n        return apiClient.post('/customer/analyze', data);\n    },\n    // 获取客户洞察\n    async getInsights (userId) {\n        return apiClient.get(`/customer/insights/${userId}`);\n    },\n    // 获取个性化推荐\n    async getRecommendations (userId) {\n        return apiClient.get(`/customer/recommendations/${userId}`);\n    },\n    // 更新客户画像\n    async updateProfile (userId, profileData) {\n        return apiClient.put(`/customer/profile/${userId}`, profileData);\n    },\n    // 基于需求更新画像\n    async updateFromRequirements (userId, requirementData) {\n        return apiClient.post(`/customer/requirements/${userId}`, requirementData);\n    },\n    // 网页分析\n    async analyzeWebProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-url', data);\n    },\n    // 文本分析\n    async analyzeTextProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-text', data);\n    },\n    // 获取分析历史\n    async getAnalysisHistory (page = 1, size = 20) {\n        return apiClient.get(`/profile-analysis/analysis-history?page=${page}&size=${size}`);\n    }\n};\n// 智能文献搜索API\nconst smartLiteratureApi = {\n    // 生成智能搜索查询\n    async generateSmartQueries (data) {\n        return apiClient.post('/smart-literature/generate-smart-queries', data);\n    },\n    // Perplexity风格搜索\n    async perplexitySearch (data) {\n        return apiClient.post('/smart-literature/perplexity-search', data);\n    },\n    // 增强文献搜索\n    async enhancedLiteratureSearch (data) {\n        return apiClient.post('/smart-literature/enhanced-literature-search', data);\n    }\n};\n// 方案生成API\nconst solutionApi = {\n    // 生成完整方案\n    async generateSolution (data) {\n        return apiClient.post('/solution/generate-solution', data);\n    },\n    // 快速成本估算\n    async quickEstimate (requirements) {\n        return apiClient.post('/solution/quick-estimate', requirements);\n    },\n    // 获取方案模板\n    async getSolutionTemplates () {\n        return apiClient.get('/solution/solution-templates');\n    },\n    // 验证需求完整性\n    async validateRequirements (requirements) {\n        return apiClient.post('/solution/validate-requirements', requirements);\n    },\n    // 获取平台对比\n    async getPlatformComparison () {\n        return apiClient.get('/solution/platform-comparison');\n    },\n    // 生成综合方案框架（按设计文档要求）\n    async generateComprehensiveFramework (data) {\n        return apiClient.post('/solution/comprehensive-framework', {\n            requirements: data.requirements,\n            user_message: data.user_message || '',\n            framework_template: data.framework_template || 'standard',\n            enable_literature_search: data.enable_literature_search !== false\n        });\n    }\n};\n// 智能推荐API\nconst intelligentRecommendationApi = {\n    // 获取综合智能推荐\n    async getComprehensiveRecommendation (data) {\n        return apiClient.post('/intelligent-recommendation/comprehensive-recommendation', data);\n    },\n    // 获取智能关键词扩展\n    async getKeywordExpansion (data) {\n        return apiClient.post('/intelligent-recommendation/keyword-expansion', data);\n    },\n    // 获取热点文献推荐\n    async getHotPapers (data) {\n        return apiClient.post('/intelligent-recommendation/hot-papers', data);\n    },\n    // 获取技术平台推荐\n    async getTechRecommendations (data) {\n        return apiClient.post('/intelligent-recommendation/tech-recommendations', data);\n    },\n    // 获取项目整体解决方案\n    async getProjectSolution (data) {\n        return apiClient.post('/intelligent-recommendation/project-solution', data);\n    },\n    // 健康检查\n    async healthCheck () {\n        return apiClient.get('/intelligent-recommendation/health');\n    }\n};\n// 导出Token管理器和API客户端\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvYXBpLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBOztDQUVDLEdBRUQsZUFBZTtBQUNmLE1BQU1BLGdCQUFnQjtJQUNwQixXQUFXO0lBQ1gsSUFBSUMsUUFBUUMsR0FBRyxDQUFDQyxtQkFBbUIsRUFBRTtRQUNuQyxPQUFPRixRQUFRQyxHQUFHLENBQUNDLG1CQUFtQjtJQUN4QztJQUVBLDJCQUEyQjtJQUMzQixJQUFJLEtBQTZCLEVBQUUsRUFJbEM7SUFFRCxhQUFhO0lBQ2IsT0FBTztBQUNUO0FBRUEsTUFBTUssZUFBZVI7QUFzRXJCLFVBQVU7QUFDVixNQUFNUzs7YUFDb0JDLFlBQVk7OzthQUNaQyxjQUFjOztJQUV0QyxPQUFPQyxXQUEwQjtRQUMvQixJQUFJLElBQTZCLEVBQUUsT0FBTztRQUMxQyxPQUFPQyxhQUFhQyxPQUFPLENBQUMsSUFBSSxDQUFDSixTQUFTO0lBQzVDO0lBRUEsT0FBT0ssU0FBU0MsS0FBYSxFQUFRO1FBQ25DLElBQUksSUFBNkIsRUFBRTtRQUNuQ0gsYUFBYUksT0FBTyxDQUFDLElBQUksQ0FBQ1AsU0FBUyxFQUFFTTtJQUN2QztJQUVBLE9BQU9FLGNBQW9CO1FBQ3pCLElBQUksSUFBNkIsRUFBRTtRQUNuQ0wsYUFBYU0sVUFBVSxDQUFDLElBQUksQ0FBQ1QsU0FBUztRQUN0Q0csYUFBYU0sVUFBVSxDQUFDLElBQUksQ0FBQ1IsV0FBVztJQUMxQztJQUVBLE9BQU9TLGVBQWVKLEtBQWEsRUFBVztRQUM1QyxJQUFJO1lBQ0YsTUFBTUssVUFBVUMsS0FBS0MsS0FBSyxDQUFDQyxLQUFLUixNQUFNUyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDbkQsT0FBT0osUUFBUUssR0FBRyxHQUFHLE9BQU9DLEtBQUtDLEdBQUc7UUFDdEMsRUFBRSxPQUFNO1lBQ04sT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQUVBLFdBQVc7QUFDWCxNQUFNQztJQUdKQyxZQUFZQyxVQUFrQnZCLFlBQVksQ0FBRTtRQUMxQyxJQUFJLENBQUN1QixPQUFPLEdBQUdBO0lBQ2pCO0lBRUEsTUFBY0MsUUFDWkMsUUFBZ0IsRUFDaEJDLFVBQXVCLENBQUMsQ0FBQyxFQUNiO1FBQ1osTUFBTUMsTUFBTSxHQUFHLElBQUksQ0FBQ0osT0FBTyxHQUFHRSxVQUFVO1FBQ3hDLE1BQU1qQixRQUFRUCxhQUFhRyxRQUFRO1FBRW5DLE1BQU13QixVQUF1QjtZQUMzQixnQkFBZ0I7WUFDaEIsR0FBR0YsUUFBUUUsT0FBTztRQUNwQjtRQUVBLFFBQVE7UUFDUixJQUFJcEIsU0FBUyxDQUFDUCxhQUFhVyxjQUFjLENBQUNKLFFBQVE7WUFDaERvQixRQUFRQyxhQUFhLEdBQUcsQ0FBQyxPQUFPLEVBQUVyQixPQUFPO1FBQzNDO1FBRUEsTUFBTXNCLFNBQXNCO1lBQzFCLEdBQUdKLE9BQU87WUFDVkU7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNRyxXQUFXLE1BQU1DLE1BQU1MLEtBQUtHO1lBRWxDLElBQUksQ0FBQ0MsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNQyxZQUFZLE1BQU1ILFNBQVNJLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztnQkFFdEQsc0JBQXNCO2dCQUN0QixJQUFJQyxlQUFlSCxVQUFVSSxNQUFNLElBQUlKLFVBQVVLLE9BQU8sSUFBSSxDQUFDLEtBQUssRUFBRVIsU0FBU1MsTUFBTSxDQUFDLEVBQUUsRUFBRVQsU0FBU1UsVUFBVSxFQUFFO2dCQUU3RyxJQUFJVixTQUFTUyxNQUFNLEtBQUssS0FBSztvQkFDM0JILGVBQWVILFVBQVVJLE1BQU0sSUFBSTtnQkFDckMsT0FBTyxJQUFJUCxTQUFTUyxNQUFNLEtBQUssS0FBSztvQkFDbENILGVBQWVILFVBQVVJLE1BQU0sSUFBSTtnQkFDckMsT0FBTyxJQUFJUCxTQUFTUyxNQUFNLEtBQUssS0FBSztvQkFDbENILGVBQWU7Z0JBQ2pCLE9BQU8sSUFBSU4sU0FBU1MsTUFBTSxLQUFLLEtBQUs7b0JBQ2xDSCxlQUFlO2dCQUNqQixPQUFPLElBQUlOLFNBQVNTLE1BQU0sSUFBSSxLQUFLO29CQUNqQ0gsZUFBZTtnQkFDakI7Z0JBRUEsTUFBTSxJQUFJSyxNQUFNTDtZQUNsQjtZQUVBLE9BQU8sTUFBTU4sU0FBU0ksSUFBSTtRQUM1QixFQUFFLE9BQU9RLE9BQU87WUFDZCxTQUFTO1lBQ1QsSUFBSUEsaUJBQWlCQyxhQUFhRCxNQUFNSixPQUFPLENBQUNNLFFBQVEsQ0FBQyxVQUFVO2dCQUNqRSxNQUFNLElBQUlILE1BQU07WUFDbEI7WUFFQUksUUFBUUgsS0FBSyxDQUFDLFlBQVlBO1lBQzFCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLFFBQVE7SUFDUixNQUFNSSxJQUFPdEIsUUFBZ0IsRUFBYztRQUN6QyxPQUFPLElBQUksQ0FBQ0QsT0FBTyxDQUFJQyxVQUFVO1lBQUV1QixRQUFRO1FBQU07SUFDbkQ7SUFFQSxTQUFTO0lBQ1QsTUFBTUMsS0FBUXhCLFFBQWdCLEVBQUV5QixJQUFVLEVBQWM7UUFDdEQsT0FBTyxJQUFJLENBQUMxQixPQUFPLENBQUlDLFVBQVU7WUFDL0J1QixRQUFRO1lBQ1JHLE1BQU1ELE9BQU9wQyxLQUFLc0MsU0FBUyxDQUFDRixRQUFRRztRQUN0QztJQUNGO0lBRUEsUUFBUTtJQUNSLE1BQU1DLElBQU83QixRQUFnQixFQUFFeUIsSUFBVSxFQUFjO1FBQ3JELE9BQU8sSUFBSSxDQUFDMUIsT0FBTyxDQUFJQyxVQUFVO1lBQy9CdUIsUUFBUTtZQUNSRyxNQUFNRCxPQUFPcEMsS0FBS3NDLFNBQVMsQ0FBQ0YsUUFBUUc7UUFDdEM7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNRSxPQUFVOUIsUUFBZ0IsRUFBYztRQUM1QyxPQUFPLElBQUksQ0FBQ0QsT0FBTyxDQUFJQyxVQUFVO1lBQUV1QixRQUFRO1FBQVM7SUFDdEQ7QUFDRjtBQUVBLGFBQWE7QUFDYixNQUFNUSxZQUFZLElBQUluQztBQUV0QixRQUFRO0FBQ0QsTUFBTW9DLFVBQVU7SUFDckIsT0FBTztJQUNQLE1BQU1DLE9BQU1DLFdBQXlCO1FBQ25DLE1BQU01QixXQUFXLE1BQU15QixVQUFVUCxJQUFJLENBQWdCLGVBQWVVO1FBQ3BFMUQsYUFBYU0sUUFBUSxDQUFDd0IsU0FBUzZCLFlBQVk7UUFDM0MsT0FBTzdCO0lBQ1Q7SUFFQSxPQUFPO0lBQ1AsTUFBTThCLFVBQVNDLFFBQXlCO1FBQ3RDLE9BQU9OLFVBQVVQLElBQUksQ0FBYyxrQkFBa0JhO0lBQ3ZEO0lBRUEsV0FBVztJQUNYLE1BQU1DO1FBQ0osT0FBT1AsVUFBVVQsR0FBRyxDQUFPO0lBQzdCO0lBRUEsT0FBTztJQUNQLE1BQU1pQjtRQUNKLE1BQU1qQyxXQUFXLE1BQU15QixVQUFVUCxJQUFJLENBQWdCO1FBQ3JEaEQsYUFBYU0sUUFBUSxDQUFDd0IsU0FBUzZCLFlBQVk7UUFDM0MsT0FBTzdCO0lBQ1Q7SUFFQSxPQUFPO0lBQ1AsTUFBTWtDLGdCQUFlZixJQUlwQjtRQUNDLE9BQU9NLFVBQVVQLElBQUksQ0FBYyx5QkFBeUJDO0lBQzlEO0lBRUEsS0FBSztJQUNMZ0I7UUFDRWpFLGFBQWFTLFdBQVc7SUFDMUI7QUFDRixFQUFDO0FBRUQsUUFBUTtBQUNELE1BQU15RCxrQkFBa0I7SUFDN0IsT0FBTztJQUNQLE1BQU1DLGFBQVlsQixJQU1qQjtRQU9DLE9BQU9NLFVBQVVQLElBQUksQ0FBQyxzQkFBc0JDO0lBQzlDO0lBRUEsU0FBUztJQUNULE1BQU1tQixZQUFXQyxPQUFlLENBQUMsRUFBRUMsT0FBZSxFQUFFO1FBTWxELE9BQU9mLFVBQVVULEdBQUcsQ0FBQyxDQUFDLDJCQUEyQixFQUFFdUIsS0FBSyxNQUFNLEVBQUVDLE1BQU07SUFDeEU7QUFDRixFQUFDO0FBRUQsUUFBUTtBQUNELE1BQU1DLGdCQUFnQjtJQUMzQixPQUFPO0lBQ1AsTUFBTUMsa0JBQWlCdkIsSUFLdEI7UUFtQkMsT0FBT00sVUFBVVAsSUFBSSxDQUFDLHNCQUFzQkM7SUFDOUM7SUFFQSxTQUFTO0lBQ1QsTUFBTXdCLDhCQUE2QnhCLElBSWxDO1FBT0MsT0FBT00sVUFBVVAsSUFBSSxDQUFDLCtCQUErQkM7SUFDdkQ7SUFFQSxTQUFTO0lBQ1QsTUFBTXlCO1FBS0osT0FBT25CLFVBQVVULEdBQUcsQ0FBQztJQUN2QjtJQUVBLFNBQVM7SUFDVCxNQUFNNkIsdUJBQXNCQyxRQUFnQixFQUFFO1FBSTVDLE9BQU9yQixVQUFVVCxHQUFHLENBQUMsQ0FBQywyQkFBMkIsRUFBRThCLE9BQU87SUFDNUQ7SUFFQSxTQUFTO0lBQ1QsTUFBTUM7UUFRSixPQUFPdEIsVUFBVVQsR0FBRyxDQUFDO0lBQ3ZCO0lBRUEsV0FBVztJQUNYLE1BQU1nQztRQVlKLE9BQU92QixVQUFVVCxHQUFHLENBQUM7SUFDdkI7SUFFQSxXQUFXO0lBQ1gsTUFBTWlDLHdCQUF1QjlCLElBSTVCO1FBYUMsT0FBT00sVUFBVVAsSUFBSSxDQUFDLHdDQUF3Q0M7SUFDaEU7QUFDRixFQUFDO0FBc0ZELFVBQVU7QUFDSCxNQUFNK0IscUJBQXFCO0lBQ2hDLFNBQVM7SUFDVCxNQUFNQyxZQUFXQyxNQUFjO1FBQzdCLE9BQU8zQixVQUFVVCxHQUFHLENBQWtCLENBQUMsa0JBQWtCLEVBQUVvQyxRQUFRO0lBQ3JFO0lBRUEsU0FBUztJQUNULE1BQU1DLGdCQUFlbEMsSUFJcEI7UUFLQyxPQUFPTSxVQUFVUCxJQUFJLENBQUMscUJBQXFCQztJQUM3QztJQUVBLFNBQVM7SUFDVCxNQUFNbUMsYUFBWUYsTUFBYztRQUM5QixPQUFPM0IsVUFBVVQsR0FBRyxDQUFtQixDQUFDLG1CQUFtQixFQUFFb0MsUUFBUTtJQUN2RTtJQUVBLFVBQVU7SUFDVixNQUFNRyxvQkFBbUJILE1BQWM7UUFDckMsT0FBTzNCLFVBQVVULEdBQUcsQ0FBOEIsQ0FBQywwQkFBMEIsRUFBRW9DLFFBQVE7SUFDekY7SUFFQSxTQUFTO0lBQ1QsTUFBTUksZUFBY0osTUFBYyxFQUFFSyxXQUFxQztRQUN2RSxPQUFPaEMsVUFBVUYsR0FBRyxDQUFrQixDQUFDLGtCQUFrQixFQUFFNkIsUUFBUSxFQUFFSztJQUN2RTtJQUVBLFdBQVc7SUFDWCxNQUFNQyx3QkFBdUJOLE1BQWMsRUFBRU8sZUFBb0M7UUFDL0UsT0FBT2xDLFVBQVVQLElBQUksQ0FBc0IsQ0FBQyx1QkFBdUIsRUFBRWtDLFFBQVEsRUFBRU87SUFDakY7SUFFQSxPQUFPO0lBQ1AsTUFBTUMsbUJBQWtCekMsSUFBK0I7UUFPckQsT0FBT00sVUFBVVAsSUFBSSxDQUFDLGlDQUFpQ0M7SUFDekQ7SUFFQSxPQUFPO0lBQ1AsTUFBTTBDLG9CQUFtQjFDLElBQWdDO1FBT3ZELE9BQU9NLFVBQVVQLElBQUksQ0FBQyxrQ0FBa0NDO0lBQzFEO0lBRUEsU0FBUztJQUNULE1BQU0yQyxvQkFBbUJ2QixPQUFlLENBQUMsRUFBRUMsT0FBZSxFQUFFO1FBYTFELE9BQU9mLFVBQVVULEdBQUcsQ0FBQyxDQUFDLHdDQUF3QyxFQUFFdUIsS0FBSyxNQUFNLEVBQUVDLE1BQU07SUFDckY7QUFDRixFQUFDO0FBRUQsWUFBWTtBQUNMLE1BQU11QixxQkFBcUI7SUFDaEMsV0FBVztJQUNYLE1BQU1DLHNCQUFxQjdDLElBSTFCO1FBUUMsT0FBT00sVUFBVVAsSUFBSSxDQUFDLDRDQUE0Q0M7SUFDcEU7SUFFQSxpQkFBaUI7SUFDakIsTUFBTThDLGtCQUFpQjlDLElBS3RCO1FBU0MsT0FBT00sVUFBVVAsSUFBSSxDQUFDLHVDQUF1Q0M7SUFDL0Q7SUFFQSxTQUFTO0lBQ1QsTUFBTStDLDBCQUF5Qi9DLElBSzlCO1FBUUMsT0FBT00sVUFBVVAsSUFBSSxDQUFDLGdEQUFnREM7SUFDeEU7QUFDRixFQUFDO0FBRUQsVUFBVTtBQUNILE1BQU1nRCxjQUFjO0lBQ3pCLFNBQVM7SUFDVCxNQUFNQyxrQkFBaUJqRCxJQUt0QjtRQVlDLE9BQU9NLFVBQVVQLElBQUksQ0FBQywrQkFBK0JDO0lBQ3ZEO0lBRUEsU0FBUztJQUNULE1BQU1rRCxlQUFjQyxZQUFpQztRQVduRCxPQUFPN0MsVUFBVVAsSUFBSSxDQUFDLDRCQUE0Qm9EO0lBQ3BEO0lBRUEsU0FBUztJQUNULE1BQU1DO1FBS0osT0FBTzlDLFVBQVVULEdBQUcsQ0FBQztJQUN2QjtJQUVBLFVBQVU7SUFDVixNQUFNd0Qsc0JBQXFCRixZQUFpQztRQVUxRCxPQUFPN0MsVUFBVVAsSUFBSSxDQUFDLG1DQUFtQ29EO0lBQzNEO0lBRUEsU0FBUztJQUNULE1BQU1HO1FBS0osT0FBT2hELFVBQVVULEdBQUcsQ0FBQztJQUN2QjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNMEQsZ0NBQStCdkQsSUFLcEM7UUFpQkMsT0FBT00sVUFBVVAsSUFBSSxDQUFDLHFDQUFxQztZQUN6RG9ELGNBQWNuRCxLQUFLbUQsWUFBWTtZQUMvQkssY0FBY3hELEtBQUt3RCxZQUFZLElBQUk7WUFDbkNDLG9CQUFvQnpELEtBQUt5RCxrQkFBa0IsSUFBSTtZQUMvQ0MsMEJBQTBCMUQsS0FBSzBELHdCQUF3QixLQUFLO1FBQzlEO0lBQ0Y7QUFDRixFQUFDO0FBRUQsVUFBVTtBQUNILE1BQU1DLCtCQUErQjtJQUMxQyxXQUFXO0lBQ1gsTUFBTUMsZ0NBQStCNUQsSUFrQnBDO1FBNkVDLE9BQU9NLFVBQVVQLElBQUksQ0FBQyw0REFBNERDO0lBQ3BGO0lBRUEsWUFBWTtJQUNaLE1BQU02RCxxQkFBb0I3RCxJQUFTO1FBYWpDLE9BQU9NLFVBQVVQLElBQUksQ0FBQyxpREFBaURDO0lBQ3pFO0lBRUEsV0FBVztJQUNYLE1BQU04RCxjQUFhOUQsSUFBUztRQWtCMUIsT0FBT00sVUFBVVAsSUFBSSxDQUFDLDBDQUEwQ0M7SUFDbEU7SUFFQSxXQUFXO0lBQ1gsTUFBTStELHdCQUF1Qi9ELElBQVM7UUFrQnBDLE9BQU9NLFVBQVVQLElBQUksQ0FBQyxvREFBb0RDO0lBQzVFO0lBRUEsYUFBYTtJQUNiLE1BQU1nRSxvQkFBbUJoRSxJQUFTO1FBbUJoQyxPQUFPTSxVQUFVUCxJQUFJLENBQUMsZ0RBQWdEQztJQUN4RTtJQUVBLE9BQU87SUFDUCxNQUFNaUU7UUFLSixPQUFPM0QsVUFBVVQsR0FBRyxDQUFDO0lBQ3ZCO0FBQ0YsRUFBQztBQUVELG9CQUFvQjtBQUNjIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNjYWlcXERlc2t0b3BcXERldlxcQ2VsbEZvcmdlIEFJXFxmcm9udGVuZFxcbGliXFxhcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBUEnosIPnlKjlt6Xlhbflh73mlbBcbiAqL1xuXG4vLyDliqjmgIHojrflj5ZBUEnln7rnoYBVUkxcbmNvbnN0IGdldEFwaUJhc2VVcmwgPSAoKTogc3RyaW5nID0+IHtcbiAgLy8g5LyY5YWI5L2/55So546v5aKD5Y+Y6YePXG4gIGlmIChwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMKSB7XG4gICAgcmV0dXJuIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkxcbiAgfVxuXG4gIC8vIOWcqOa1j+iniOWZqOeOr+Wig+S4re+8jOS9v+eUqOW9k+WJjeS4u+acuueahElQ5ZKM56uv5Y+jODAwMFxuICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBjb25zdCBwcm90b2NvbCA9IHdpbmRvdy5sb2NhdGlvbi5wcm90b2NvbFxuICAgIGNvbnN0IGhvc3RuYW1lID0gd2luZG93LmxvY2F0aW9uLmhvc3RuYW1lXG4gICAgcmV0dXJuIGAke3Byb3RvY29sfS8vJHtob3N0bmFtZX06ODAwMC9hcGkvdjFgXG4gIH1cblxuICAvLyDmnI3liqHnq6/muLLmn5Pml7bnmoTpu5jorqTlgLxcbiAgcmV0dXJuICdodHRwOi8vbG9jYWxob3N0OjgwMDAvYXBpL3YxJ1xufVxuXG5jb25zdCBBUElfQkFTRV9VUkwgPSBnZXRBcGlCYXNlVXJsKClcblxuLy8gQVBJ5ZON5bqU57G75Z6LXG5leHBvcnQgaW50ZXJmYWNlIEFwaVJlc3BvbnNlPFQgPSBhbnk+IHtcbiAgc3RhdHVzOiAnc3VjY2VzcycgfCAnZXJyb3InIHwgJ3dhcm5pbmcnXG4gIG1lc3NhZ2U6IHN0cmluZ1xuICBkYXRhPzogVFxuICB0aW1lc3RhbXA6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEFwaUVycm9yIHtcbiAgc3RhdHVzOiAnZXJyb3InXG4gIG1lc3NhZ2U6IHN0cmluZ1xuICBlcnJvcl9jb2RlPzogc3RyaW5nXG4gIGRldGFpbHM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gIHRpbWVzdGFtcDogc3RyaW5nXG59XG5cbi8vIOeUqOaIt+ebuOWFs+exu+Wei1xuZXhwb3J0IGludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IG51bWJlclxuICBlbWFpbDogc3RyaW5nXG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgZmlyc3RfbmFtZT86IHN0cmluZ1xuICBsYXN0X25hbWU/OiBzdHJpbmdcbiAgcGhvbmU/OiBzdHJpbmdcbiAgb3JnYW5pemF0aW9uPzogc3RyaW5nXG4gIGRlcGFydG1lbnQ/OiBzdHJpbmdcbiAgdGl0bGU/OiBzdHJpbmdcbiAgcm9sZTogJ3N1cGVyX2FkbWluJyB8ICdzYWxlcycgfCAnY3VzdG9tZXInIHwgJ29wZXJhdGlvbnMnIHwgJ2d1ZXN0J1xuICBzdGF0dXM6ICdhY3RpdmUnIHwgJ3BlbmRpbmcnIHwgJ3N1c3BlbmRlZCcgfCAnZGVsZXRlZCdcbiAgaXNfdmVyaWZpZWQ6IGJvb2xlYW5cbiAgY3JlYXRlZF9hdDogc3RyaW5nXG4gIHVwZGF0ZWRfYXQ/OiBzdHJpbmdcbiAgbGFzdF9sb2dpbj86IHN0cmluZ1xuICBwcmVmZXJyZWRfbGFuZ3VhZ2U6IHN0cmluZ1xuICBlbWFpbF9ub3RpZmljYXRpb25zOiBib29sZWFuXG4gIHJlc2VhcmNoX2ludGVyZXN0cz86IHN0cmluZ1xuICBleHBlcnRpc2VfYXJlYXM/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBMb2dpblJlcXVlc3Qge1xuICBlbWFpbDogc3RyaW5nXG4gIHBhc3N3b3JkOiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSZWdpc3RlclJlcXVlc3Qge1xuICBlbWFpbDogc3RyaW5nXG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgcGFzc3dvcmQ6IHN0cmluZ1xuICBjb25maXJtX3Bhc3N3b3JkOiBzdHJpbmdcbiAgZmlyc3RfbmFtZT86IHN0cmluZ1xuICBsYXN0X25hbWU/OiBzdHJpbmdcbiAgcGhvbmU/OiBzdHJpbmdcbiAgb3JnYW5pemF0aW9uPzogc3RyaW5nXG4gIGRlcGFydG1lbnQ/OiBzdHJpbmdcbiAgdGl0bGU/OiBzdHJpbmdcbiAgcm9sZT86IHN0cmluZ1xuICBwcmVmZXJyZWRfbGFuZ3VhZ2U/OiBzdHJpbmdcbiAgZW1haWxfbm90aWZpY2F0aW9ucz86IGJvb2xlYW5cbiAgcmVzZWFyY2hfaW50ZXJlc3RzPzogc3RyaW5nXG4gIGV4cGVydGlzZV9hcmVhcz86IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRva2VuUmVzcG9uc2Uge1xuICBhY2Nlc3NfdG9rZW46IHN0cmluZ1xuICB0b2tlbl90eXBlOiBzdHJpbmdcbiAgZXhwaXJlc19pbjogbnVtYmVyXG59XG5cbi8vIFRva2Vu566h55CGXG5jbGFzcyBUb2tlbk1hbmFnZXIge1xuICBwcml2YXRlIHN0YXRpYyByZWFkb25seSBUT0tFTl9LRVkgPSAnY2VsbGZvcmdlX2FjY2Vzc190b2tlbidcbiAgcHJpdmF0ZSBzdGF0aWMgcmVhZG9ubHkgUkVGUkVTSF9LRVkgPSAnY2VsbGZvcmdlX3JlZnJlc2hfdG9rZW4nXG5cbiAgc3RhdGljIGdldFRva2VuKCk6IHN0cmluZyB8IG51bGwge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIG51bGxcbiAgICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0odGhpcy5UT0tFTl9LRVkpXG4gIH1cblxuICBzdGF0aWMgc2V0VG9rZW4odG9rZW46IHN0cmluZyk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0odGhpcy5UT0tFTl9LRVksIHRva2VuKVxuICB9XG5cbiAgc3RhdGljIHJlbW92ZVRva2VuKCk6IHZvaWQge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0odGhpcy5UT0tFTl9LRVkpXG4gICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0odGhpcy5SRUZSRVNIX0tFWSlcbiAgfVxuXG4gIHN0YXRpYyBpc1Rva2VuRXhwaXJlZCh0b2tlbjogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBheWxvYWQgPSBKU09OLnBhcnNlKGF0b2IodG9rZW4uc3BsaXQoJy4nKVsxXSkpXG4gICAgICByZXR1cm4gcGF5bG9hZC5leHAgKiAxMDAwIDwgRGF0ZS5ub3coKVxuICAgIH0gY2F0Y2gge1xuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG4gIH1cbn1cblxuLy8gSFRUUOWuouaIt+err+exu1xuY2xhc3MgQXBpQ2xpZW50IHtcbiAgcHJpdmF0ZSBiYXNlVVJMOiBzdHJpbmdcblxuICBjb25zdHJ1Y3RvcihiYXNlVVJMOiBzdHJpbmcgPSBBUElfQkFTRV9VUkwpIHtcbiAgICB0aGlzLmJhc2VVUkwgPSBiYXNlVVJMXG4gIH1cblxuICBwcml2YXRlIGFzeW5jIHJlcXVlc3Q8VD4oXG4gICAgZW5kcG9pbnQ6IHN0cmluZyxcbiAgICBvcHRpb25zOiBSZXF1ZXN0SW5pdCA9IHt9XG4gICk6IFByb21pc2U8VD4ge1xuICAgIGNvbnN0IHVybCA9IGAke3RoaXMuYmFzZVVSTH0ke2VuZHBvaW50fWBcbiAgICBjb25zdCB0b2tlbiA9IFRva2VuTWFuYWdlci5nZXRUb2tlbigpXG5cbiAgICBjb25zdCBoZWFkZXJzOiBIZWFkZXJzSW5pdCA9IHtcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAuLi5vcHRpb25zLmhlYWRlcnMsXG4gICAgfVxuXG4gICAgLy8g5re75Yqg6K6k6K+B5aS0XG4gICAgaWYgKHRva2VuICYmICFUb2tlbk1hbmFnZXIuaXNUb2tlbkV4cGlyZWQodG9rZW4pKSB7XG4gICAgICBoZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YFxuICAgIH1cblxuICAgIGNvbnN0IGNvbmZpZzogUmVxdWVzdEluaXQgPSB7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgaGVhZGVycyxcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIGNvbmZpZylcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSlcblxuICAgICAgICAvLyDmoLnmja5IVFRQ54q25oCB56CB5o+Q5L6b5pu06K+m57uG55qE6ZSZ6K+v5L+h5oGvXG4gICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBlcnJvckRhdGEuZGV0YWlsIHx8IGVycm9yRGF0YS5tZXNzYWdlIHx8IGBIVFRQICR7cmVzcG9uc2Uuc3RhdHVzfTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWBcblxuICAgICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvckRhdGEuZGV0YWlsIHx8IFwi6YKu566x5oiW5a+G56CB6ZSZ6K+vXCJcbiAgICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMykge1xuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yRGF0YS5kZXRhaWwgfHwgXCLotKbmiLflt7LooqvmmoLlgZzmiJbml6DmnYPpmZDorr/pl65cIlxuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gXCLor7fmsYLnmoTotYTmupDkuI3lrZjlnKhcIlxuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDIyKSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gXCLor7fmsYLmlbDmja7moLzlvI/plJnor69cIlxuICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnN0YXR1cyA+PSA1MDApIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBcIuacjeWKoeWZqOWGhemDqOmUmeivr++8jOivt+eojeWQjumHjeivlVwiXG4gICAgICAgIH1cblxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIOe9kee7nOmUmeivr+WkhOeQhlxuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2ZldGNoJykpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfnvZHnu5zov57mjqXlpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqUnKVxuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmVycm9yKCdBUEnor7fmsYLlpLHotKU6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIC8vIEdFVOivt+axglxuICBhc3luYyBnZXQ8VD4oZW5kcG9pbnQ6IHN0cmluZyk6IFByb21pc2U8VD4ge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3Q8VD4oZW5kcG9pbnQsIHsgbWV0aG9kOiAnR0VUJyB9KVxuICB9XG5cbiAgLy8gUE9TVOivt+axglxuICBhc3luYyBwb3N0PFQ+KGVuZHBvaW50OiBzdHJpbmcsIGRhdGE/OiBhbnkpOiBQcm9taXNlPFQ+IHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0PFQ+KGVuZHBvaW50LCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IGRhdGEgPyBKU09OLnN0cmluZ2lmeShkYXRhKSA6IHVuZGVmaW5lZCxcbiAgICB9KVxuICB9XG5cbiAgLy8gUFVU6K+35rGCXG4gIGFzeW5jIHB1dDxUPihlbmRwb2ludDogc3RyaW5nLCBkYXRhPzogYW55KTogUHJvbWlzZTxUPiB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdDxUPihlbmRwb2ludCwge1xuICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgIGJvZHk6IGRhdGEgPyBKU09OLnN0cmluZ2lmeShkYXRhKSA6IHVuZGVmaW5lZCxcbiAgICB9KVxuICB9XG5cbiAgLy8gREVMRVRF6K+35rGCXG4gIGFzeW5jIGRlbGV0ZTxUPihlbmRwb2ludDogc3RyaW5nKTogUHJvbWlzZTxUPiB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdDxUPihlbmRwb2ludCwgeyBtZXRob2Q6ICdERUxFVEUnIH0pXG4gIH1cbn1cblxuLy8g5Yib5bu6QVBJ5a6i5oi356uv5a6e5L6LXG5jb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KClcblxuLy8g6K6k6K+BQVBJXG5leHBvcnQgY29uc3QgYXV0aEFwaSA9IHtcbiAgLy8g55So5oi355m75b2VXG4gIGFzeW5jIGxvZ2luKGNyZWRlbnRpYWxzOiBMb2dpblJlcXVlc3QpOiBQcm9taXNlPFRva2VuUmVzcG9uc2U+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0PFRva2VuUmVzcG9uc2U+KCcvYXV0aC9sb2dpbicsIGNyZWRlbnRpYWxzKVxuICAgIFRva2VuTWFuYWdlci5zZXRUb2tlbihyZXNwb25zZS5hY2Nlc3NfdG9rZW4pXG4gICAgcmV0dXJuIHJlc3BvbnNlXG4gIH0sXG5cbiAgLy8g55So5oi35rOo5YaMXG4gIGFzeW5jIHJlZ2lzdGVyKHVzZXJEYXRhOiBSZWdpc3RlclJlcXVlc3QpOiBQcm9taXNlPEFwaVJlc3BvbnNlPiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5wb3N0PEFwaVJlc3BvbnNlPignL2F1dGgvcmVnaXN0ZXInLCB1c2VyRGF0YSlcbiAgfSxcblxuICAvLyDojrflj5blvZPliY3nlKjmiLfkv6Hmga9cbiAgYXN5bmMgZ2V0Q3VycmVudFVzZXIoKTogUHJvbWlzZTxVc2VyPiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5nZXQ8VXNlcj4oJy9hdXRoL21lJylcbiAgfSxcblxuICAvLyDliLfmlrDku6TniYxcbiAgYXN5bmMgcmVmcmVzaFRva2VuKCk6IFByb21pc2U8VG9rZW5SZXNwb25zZT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3Q8VG9rZW5SZXNwb25zZT4oJy9hdXRoL3JlZnJlc2gnKVxuICAgIFRva2VuTWFuYWdlci5zZXRUb2tlbihyZXNwb25zZS5hY2Nlc3NfdG9rZW4pXG4gICAgcmV0dXJuIHJlc3BvbnNlXG4gIH0sXG5cbiAgLy8g5L+u5pS55a+G56CBXG4gIGFzeW5jIGNoYW5nZVBhc3N3b3JkKGRhdGE6IHtcbiAgICBjdXJyZW50X3Bhc3N3b3JkOiBzdHJpbmdcbiAgICBuZXdfcGFzc3dvcmQ6IHN0cmluZ1xuICAgIGNvbmZpcm1fcGFzc3dvcmQ6IHN0cmluZ1xuICB9KTogUHJvbWlzZTxBcGlSZXNwb25zZT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdDxBcGlSZXNwb25zZT4oJy9hdXRoL2NoYW5nZS1wYXNzd29yZCcsIGRhdGEpXG4gIH0sXG5cbiAgLy8g55m75Ye6XG4gIGxvZ291dCgpOiB2b2lkIHtcbiAgICBUb2tlbk1hbmFnZXIucmVtb3ZlVG9rZW4oKVxuICB9LFxufVxuXG4vLyDlr7nor51BUElcbmV4cG9ydCBjb25zdCBjb252ZXJzYXRpb25BcGkgPSB7XG4gIC8vIOWPkemAgea2iOaBr1xuICBhc3luYyBzZW5kTWVzc2FnZShkYXRhOiB7XG4gICAgbWVzc2FnZTogc3RyaW5nXG4gICAgY29udmVyc2F0aW9uX2lkPzogbnVtYmVyXG4gICAgY29udmVyc2F0aW9uX3R5cGU/OiBzdHJpbmdcbiAgICBoaXN0b3J5PzogQXJyYXk8eyByb2xlOiBzdHJpbmc7IGNvbnRlbnQ6IHN0cmluZyB9PlxuICAgIGNvbnRleHQ/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gIH0pOiBQcm9taXNlPHtcbiAgICBtZXNzYWdlOiBzdHJpbmdcbiAgICBjb25maWRlbmNlOiBudW1iZXJcbiAgICBzb3VyY2VzPzogc3RyaW5nW11cbiAgICBzdWdnZXN0aW9ucz86IHN0cmluZ1tdXG4gICAgdGltZXN0YW1wOiBzdHJpbmdcbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdCgnL2NvbnZlcnNhdGlvbi9jaGF0JywgZGF0YSlcbiAgfSxcblxuICAvLyDojrflj5blr7nor53ljoblj7JcbiAgYXN5bmMgZ2V0SGlzdG9yeShwYWdlOiBudW1iZXIgPSAxLCBzaXplOiBudW1iZXIgPSAyMCk6IFByb21pc2U8e1xuICAgIGNvbnZlcnNhdGlvbnM6IGFueVtdXG4gICAgdG90YWw6IG51bWJlclxuICAgIHBhZ2U6IG51bWJlclxuICAgIHNpemU6IG51bWJlclxuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5nZXQoYC9jb252ZXJzYXRpb24vaGlzdG9yeT9wYWdlPSR7cGFnZX0mc2l6ZT0ke3NpemV9YClcbiAgfSxcbn1cblxuLy8g5paH54yuQVBJXG5leHBvcnQgY29uc3QgbGl0ZXJhdHVyZUFwaSA9IHtcbiAgLy8g5pCc57Si5paH54yuXG4gIGFzeW5jIHNlYXJjaExpdGVyYXR1cmUoZGF0YToge1xuICAgIHF1ZXJ5OiBzdHJpbmdcbiAgICBjYXRlZ29yeT86IHN0cmluZ1xuICAgIHRlY2hub2xvZ3lfdGFncz86IHN0cmluZ1tdXG4gICAgdG9wX2s/OiBudW1iZXJcbiAgfSk6IFByb21pc2U8e1xuICAgIGlkPzogbnVtYmVyXG4gICAgdGl0bGU6IHN0cmluZ1xuICAgIGF1dGhvcnM6IHN0cmluZ1tdXG4gICAgam91cm5hbDogc3RyaW5nXG4gICAgcHVibGljYXRpb25feWVhcjogbnVtYmVyXG4gICAgZG9pPzogc3RyaW5nXG4gICAgcHVibWVkX2lkPzogc3RyaW5nXG4gICAgYWJzdHJhY3Q6IHN0cmluZ1xuICAgIGNhdGVnb3J5OiBzdHJpbmdcbiAgICB0ZWNobm9sb2d5X3RhZ3M6IHN0cmluZ1tdXG4gICAgYXBwbGljYXRpb25fdGFnczogc3RyaW5nW11cbiAgICBpbXBhY3RfZmFjdG9yPzogbnVtYmVyXG4gICAgY2l0YXRpb25fY291bnQ6IG51bWJlclxuICAgIHJlbGV2YW5jZV9zY29yZTogbnVtYmVyXG4gICAga2V5X2ZpbmRpbmdzOiBzdHJpbmdcbiAgICBtZXRob2RvbG9neV9zdW1tYXJ5OiBzdHJpbmdcbiAgICBidXNpbmVzc192YWx1ZTogc3RyaW5nXG4gIH1bXT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdCgnL2xpdGVyYXR1cmUvc2VhcmNoJywgZGF0YSlcbiAgfSxcblxuICAvLyDojrflj5bmlofnjK7mjqjojZBcbiAgYXN5bmMgZ2V0TGl0ZXJhdHVyZVJlY29tbWVuZGF0aW9ucyhkYXRhOiB7XG4gICAgY29udGV4dDogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIHJlY29tbWVuZGF0aW9uX3R5cGU/OiBzdHJpbmdcbiAgICB0b3Bfaz86IG51bWJlclxuICB9KTogUHJvbWlzZTx7XG4gICAgbGl0ZXJhdHVyZTogYW55XG4gICAgcmVjb21tZW5kYXRpb25fdHlwZTogc3RyaW5nXG4gICAgcmVsZXZhbmNlX2V4cGxhbmF0aW9uOiBzdHJpbmdcbiAgICBidXNpbmVzc192YWx1ZTogc3RyaW5nXG4gICAga2V5X3N1cHBvcnRfcG9pbnRzOiBzdHJpbmdbXVxuICB9W10+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9saXRlcmF0dXJlL3JlY29tbWVuZGF0aW9ucycsIGRhdGEpXG4gIH0sXG5cbiAgLy8g6I635Y+W5paH54yu5YiG57G7XG4gIGFzeW5jIGdldENhdGVnb3JpZXMoKTogUHJvbWlzZTx7XG4gICAgY2F0ZWdvcmllczogQXJyYXk8eyB2YWx1ZTogc3RyaW5nOyBsYWJlbDogc3RyaW5nIH0+XG4gICAgdGVjaG5vbG9neV90YWdzOiBBcnJheTx7IHZhbHVlOiBzdHJpbmc7IGxhYmVsOiBzdHJpbmcgfT5cbiAgICBhcHBsaWNhdGlvbl90YWdzOiBBcnJheTx7IHZhbHVlOiBzdHJpbmc7IGxhYmVsOiBzdHJpbmcgfT5cbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQuZ2V0KCcvbGl0ZXJhdHVyZS9jYXRlZ29yaWVzJylcbiAgfSxcblxuICAvLyDojrflj5bng63pl6jmlofnjK5cbiAgYXN5bmMgZ2V0VHJlbmRpbmdMaXRlcmF0dXJlKGxpbWl0OiBudW1iZXIgPSAxMCk6IFByb21pc2U8e1xuICAgIHRyZW5kaW5nX2xpdGVyYXR1cmU6IGFueVtdXG4gICAgdG90YWxfY291bnQ6IG51bWJlclxuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5nZXQoYC9saXRlcmF0dXJlL3RyZW5kaW5nP2xpbWl0PSR7bGltaXR9YClcbiAgfSxcblxuICAvLyDojrflj5bmlofnjK7nu5/orqFcbiAgYXN5bmMgZ2V0U3RhdHMoKTogUHJvbWlzZTx7XG4gICAgdG90YWxfbGl0ZXJhdHVyZTogbnVtYmVyXG4gICAgY2F0ZWdvcmllczogUmVjb3JkPHN0cmluZywgbnVtYmVyPlxuICAgIHRlY2hub2xvZ3lfY292ZXJhZ2U6IFJlY29yZDxzdHJpbmcsIG51bWJlcj5cbiAgICBhdmVyYWdlX2ltcGFjdF9mYWN0b3I6IG51bWJlclxuICAgIHRvdGFsX2NpdGF0aW9uczogbnVtYmVyXG4gICAgbGFzdF91cGRhdGVkOiBzdHJpbmdcbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQuZ2V0KCcvbGl0ZXJhdHVyZS9zdGF0cycpXG4gIH0sXG5cbiAgLy8g6I635Y+W5paH54yu5pCc57Si54q25oCBXG4gIGFzeW5jIGdldFNlYXJjaFN0YXR1cygpOiBQcm9taXNlPHtcbiAgICBsaXRlcmF0dXJlX3NlYXJjaF9lbmFibGVkOiBib29sZWFuXG4gICAgaXNfcmVhZHk6IGJvb2xlYW5cbiAgICBhdmFpbGFibGVfYXBpczogc3RyaW5nW11cbiAgICBhdmFpbGFibGVfY291bnQ6IG51bWJlclxuICAgIHRvdGFsX2FwaXM6IG51bWJlclxuICAgIHJlY29tbWVuZGF0aW9uczogc3RyaW5nW11cbiAgICBzZXJ2aWNlX3N0YXR1czoge1xuICAgICAgb3B0aW9uYWxfc2VydmljZV9hdmFpbGFibGU6IGJvb2xlYW5cbiAgICAgIHNlcnZpY2Vfc3RhdHVzOiBhbnlcbiAgICB9XG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LmdldCgnL2xpdGVyYXR1cmUvc2VhcmNoLXN0YXR1cycpXG4gIH0sXG5cbiAgLy8g5Z+65LqO6ZyA5rGC5pCc6ZuG5paH54yuXG4gIGFzeW5jIGNvbGxlY3RGb3JSZXF1aXJlbWVudHMoZGF0YToge1xuICAgIHJlcXVpcmVtZW50czogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIHVzZXJfcHJlZmVyZW5jZXM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgZW5hYmxlX2xpdGVyYXR1cmVfc2VhcmNoOiBib29sZWFuXG4gIH0pOiBQcm9taXNlPHtcbiAgICBzdGF0dXM6IHN0cmluZ1xuICAgIGVuYWJsZWQ6IGJvb2xlYW5cbiAgICBjb2xsZWN0aW9uX3RpbWVzdGFtcD86IHN0cmluZ1xuICAgIHNlYXJjaF9zdHJhdGVneT86IGFueVxuICAgIHRvdGFsX2ZvdW5kPzogbnVtYmVyXG4gICAgZXZhbHVhdGVkX2NvdW50PzogbnVtYmVyXG4gICAgc3VwcG9ydGluZ19wYXBlcnM/OiBhbnlbXVxuICAgIHBvdGVudGlhbF9wYXBlcnM/OiBhbnlbXVxuICAgIHNlYXJjaF9zdW1tYXJ5PzogYW55XG4gICAgbWVzc2FnZT86IHN0cmluZ1xuICAgIHJlY29tbWVuZGF0aW9ucz86IHN0cmluZ1tdXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9saXRlcmF0dXJlL2NvbGxlY3QtZm9yLXJlcXVpcmVtZW50cycsIGRhdGEpXG4gIH1cbn1cblxuLy8g5a6i5oi355S75YOP55u45YWz57G75Z6LXG5leHBvcnQgaW50ZXJmYWNlIEN1c3RvbWVyUHJvZmlsZSB7XG4gIGlkOiBudW1iZXJcbiAgdXNlcl9pZDogbnVtYmVyXG4gIHByb2ZpbGVfdmVyc2lvbjogc3RyaW5nXG4gIGxhc3RfdXBkYXRlZDogc3RyaW5nXG4gIGNvbmZpZGVuY2Vfc2NvcmU6IG51bWJlclxuICBjb21wbGV0ZW5lc3Nfc2NvcmU6IG51bWJlclxuXG4gIC8vIOeglOeptueUu+WDj1xuICByZXNlYXJjaF9tYXR1cml0eT86IHN0cmluZ1xuICByZXNlYXJjaF9mb2N1cz86IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgcHVibGljYXRpb25fbGV2ZWw/OiBzdHJpbmdcbiAgY29sbGFib3JhdGlvbl9wcmVmZXJlbmNlPzogc3RyaW5nXG5cbiAgLy8g5oqA5pyv55S75YOPXG4gIHRlY2huaWNhbF9leHBlcnRpc2U/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gIHBsYXRmb3JtX3ByZWZlcmVuY2U/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gIGFuYWx5c2lzX2NvbXBsZXhpdHk/OiBzdHJpbmdcbiAgZGF0YV9oYW5kbGluZ19jYXBhYmlsaXR5Pzogc3RyaW5nXG5cbiAgLy8g5ZWG5Lia55S75YOPXG4gIGJ1ZGdldF9yYW5nZT86IHN0cmluZ1xuICBkZWNpc2lvbl9hdXRob3JpdHk/OiBzdHJpbmdcbiAgY29zdF9zZW5zaXRpdml0eT86IHN0cmluZ1xuICBwcm9qZWN0X3NjYWxlPzogc3RyaW5nXG5cbiAgLy8g6KGM5Li655S75YOPXG4gIGVuZ2FnZW1lbnRfbGV2ZWw/OiBzdHJpbmdcbiAgbGVhcm5pbmdfc3R5bGU/OiBzdHJpbmdcbiAgY29tbXVuaWNhdGlvbl9wcmVmZXJlbmNlPzogc3RyaW5nXG4gIHJlc3BvbnNlX3NwZWVkPzogc3RyaW5nXG5cbiAgLy8g5YGP5aW955S75YOPXG4gIGNvbnRlbnRfcHJlZmVyZW5jZT86IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgaW50ZXJhY3Rpb25fc3R5bGU/OiBzdHJpbmdcbiAgaW5mb3JtYXRpb25fZGVwdGg/OiBzdHJpbmdcblxuICAvLyDpooTmtYvmgKfmjIfmoIdcbiAgY29udmVyc2lvbl9wcm9iYWJpbGl0eTogbnVtYmVyXG4gIGxpZmV0aW1lX3ZhbHVlX3ByZWRpY3Rpb246IG51bWJlclxuICBjaHVybl9yaXNrX3Njb3JlOiBudW1iZXJcbiAgbmV4dF9iZXN0X2FjdGlvbj86IFJlY29yZDxzdHJpbmcsIGFueT5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBQcm9maWxlSW5zaWdodCB7XG4gIGlkOiBudW1iZXJcbiAgYW5hbHlzaXNfaWQ6IG51bWJlclxuICBpbnNpZ2h0X3R5cGU6IHN0cmluZ1xuICB0aXRsZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgY29uZmlkZW5jZTogbnVtYmVyXG4gIGFjdGlvbmFibGVfcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXVxuICBidXNpbmVzc19pbXBhY3Q6IHN0cmluZ1xuICBwcmlvcml0eTogc3RyaW5nXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFBlcnNvbmFsaXplZFJlY29tbWVuZGF0aW9ucyB7XG4gIHVzZXJfaWQ6IG51bWJlclxuICByZWNvbW1lbmRhdGlvbnM6IEFycmF5PHtcbiAgICB0eXBlOiBzdHJpbmdcbiAgICB0aXRsZTogc3RyaW5nXG4gICAgZGVzY3JpcHRpb246IHN0cmluZ1xuICAgIHByaW9yaXR5OiBzdHJpbmdcbiAgICBlc3RpbWF0ZWRfdmFsdWU6IG51bWJlclxuICAgIGltcGxlbWVudGF0aW9uX2VmZm9ydDogc3RyaW5nXG4gICAgc3VjY2Vzc19wcm9iYWJpbGl0eTogbnVtYmVyXG4gIH0+XG4gIGdlbmVyYXRlZF9hdDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgV2ViUHJvZmlsZUFuYWx5c2lzUmVxdWVzdCB7XG4gIHVybDogc3RyaW5nXG4gIGFuYWx5c2lzX3R5cGU/OiBzdHJpbmdcbiAgZm9jdXNfYXJlYXM/OiBzdHJpbmdbXVxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRleHRQcm9maWxlQW5hbHlzaXNSZXF1ZXN0IHtcbiAgdGV4dF9jb250ZW50OiBzdHJpbmdcbiAgY29udGVudF90eXBlPzogc3RyaW5nXG4gIGFuYWx5c2lzX2RlcHRoPzogc3RyaW5nXG59XG5cbi8vIOWuouaIt+eUu+WDj0FQSVxuZXhwb3J0IGNvbnN0IGN1c3RvbWVyUHJvZmlsZUFwaSA9IHtcbiAgLy8g6I635Y+W5a6i5oi355S75YOPXG4gIGFzeW5jIGdldFByb2ZpbGUodXNlcklkOiBudW1iZXIpOiBQcm9taXNlPEN1c3RvbWVyUHJvZmlsZT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQuZ2V0PEN1c3RvbWVyUHJvZmlsZT4oYC9jdXN0b21lci9wcm9maWxlLyR7dXNlcklkfWApXG4gIH0sXG5cbiAgLy8g5YiG5p6Q5a6i5oi355S75YOPXG4gIGFzeW5jIGFuYWx5emVQcm9maWxlKGRhdGE6IHtcbiAgICB1c2VyX2lkOiBudW1iZXJcbiAgICBhbmFseXNpc190eXBlPzogc3RyaW5nXG4gICAgaW5jbHVkZV9wcmVkaWN0aW9ucz86IGJvb2xlYW5cbiAgfSk6IFByb21pc2U8e1xuICAgIHByb2ZpbGU6IEN1c3RvbWVyUHJvZmlsZVxuICAgIGluc2lnaHRzOiBQcm9maWxlSW5zaWdodFtdXG4gICAgcmVjb21tZW5kYXRpb25zOiBQZXJzb25hbGl6ZWRSZWNvbW1lbmRhdGlvbnNcbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdCgnL2N1c3RvbWVyL2FuYWx5emUnLCBkYXRhKVxuICB9LFxuXG4gIC8vIOiOt+WPluWuouaIt+a0nuWvn1xuICBhc3luYyBnZXRJbnNpZ2h0cyh1c2VySWQ6IG51bWJlcik6IFByb21pc2U8UHJvZmlsZUluc2lnaHRbXT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQuZ2V0PFByb2ZpbGVJbnNpZ2h0W10+KGAvY3VzdG9tZXIvaW5zaWdodHMvJHt1c2VySWR9YClcbiAgfSxcblxuICAvLyDojrflj5bkuKrmgKfljJbmjqjojZBcbiAgYXN5bmMgZ2V0UmVjb21tZW5kYXRpb25zKHVzZXJJZDogbnVtYmVyKTogUHJvbWlzZTxQZXJzb25hbGl6ZWRSZWNvbW1lbmRhdGlvbnM+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LmdldDxQZXJzb25hbGl6ZWRSZWNvbW1lbmRhdGlvbnM+KGAvY3VzdG9tZXIvcmVjb21tZW5kYXRpb25zLyR7dXNlcklkfWApXG4gIH0sXG5cbiAgLy8g5pu05paw5a6i5oi355S75YOPXG4gIGFzeW5jIHVwZGF0ZVByb2ZpbGUodXNlcklkOiBudW1iZXIsIHByb2ZpbGVEYXRhOiBQYXJ0aWFsPEN1c3RvbWVyUHJvZmlsZT4pOiBQcm9taXNlPEN1c3RvbWVyUHJvZmlsZT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucHV0PEN1c3RvbWVyUHJvZmlsZT4oYC9jdXN0b21lci9wcm9maWxlLyR7dXNlcklkfWAsIHByb2ZpbGVEYXRhKVxuICB9LFxuXG4gIC8vIOWfuuS6jumcgOaxguabtOaWsOeUu+WDj1xuICBhc3luYyB1cGRhdGVGcm9tUmVxdWlyZW1lbnRzKHVzZXJJZDogbnVtYmVyLCByZXF1aXJlbWVudERhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3Q8eyBtZXNzYWdlOiBzdHJpbmcgfT4oYC9jdXN0b21lci9yZXF1aXJlbWVudHMvJHt1c2VySWR9YCwgcmVxdWlyZW1lbnREYXRhKVxuICB9LFxuXG4gIC8vIOe9kemhteWIhuaekFxuICBhc3luYyBhbmFseXplV2ViUHJvZmlsZShkYXRhOiBXZWJQcm9maWxlQW5hbHlzaXNSZXF1ZXN0KTogUHJvbWlzZTx7XG4gICAgYW5hbHlzaXNfaWQ6IHN0cmluZ1xuICAgIHByb2ZpbGVfZGF0YTogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIGNvbmZpZGVuY2Vfc2NvcmU6IG51bWJlclxuICAgIGtleV9pbnNpZ2h0czogc3RyaW5nW11cbiAgICByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9wcm9maWxlLWFuYWx5c2lzL2FuYWx5emUtdXJsJywgZGF0YSlcbiAgfSxcblxuICAvLyDmlofmnKzliIbmnpBcbiAgYXN5bmMgYW5hbHl6ZVRleHRQcm9maWxlKGRhdGE6IFRleHRQcm9maWxlQW5hbHlzaXNSZXF1ZXN0KTogUHJvbWlzZTx7XG4gICAgYW5hbHlzaXNfaWQ6IHN0cmluZ1xuICAgIHByb2ZpbGVfZGF0YTogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIGNvbmZpZGVuY2Vfc2NvcmU6IG51bWJlclxuICAgIGtleV9pbnNpZ2h0czogc3RyaW5nW11cbiAgICByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9wcm9maWxlLWFuYWx5c2lzL2FuYWx5emUtdGV4dCcsIGRhdGEpXG4gIH0sXG5cbiAgLy8g6I635Y+W5YiG5p6Q5Y6G5Y+yXG4gIGFzeW5jIGdldEFuYWx5c2lzSGlzdG9yeShwYWdlOiBudW1iZXIgPSAxLCBzaXplOiBudW1iZXIgPSAyMCk6IFByb21pc2U8e1xuICAgIGFuYWx5c2VzOiBBcnJheTx7XG4gICAgICBpZDogc3RyaW5nXG4gICAgICBzb3VyY2VfdHlwZTogc3RyaW5nXG4gICAgICBzb3VyY2VfY29udGVudDogc3RyaW5nXG4gICAgICBhbmFseXNpc19yZXN1bHQ6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgIGNvbmZpZGVuY2Vfc2NvcmU6IG51bWJlclxuICAgICAgY3JlYXRlZF9hdDogc3RyaW5nXG4gICAgfT5cbiAgICB0b3RhbDogbnVtYmVyXG4gICAgcGFnZTogbnVtYmVyXG4gICAgc2l6ZTogbnVtYmVyXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LmdldChgL3Byb2ZpbGUtYW5hbHlzaXMvYW5hbHlzaXMtaGlzdG9yeT9wYWdlPSR7cGFnZX0mc2l6ZT0ke3NpemV9YClcbiAgfVxufVxuXG4vLyDmmbrog73mlofnjK7mkJzntKJBUElcbmV4cG9ydCBjb25zdCBzbWFydExpdGVyYXR1cmVBcGkgPSB7XG4gIC8vIOeUn+aIkOaZuuiDveaQnOe0ouafpeivolxuICBhc3luYyBnZW5lcmF0ZVNtYXJ0UXVlcmllcyhkYXRhOiB7XG4gICAgcmVxdWlyZW1lbnRzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgdXNlcl9xdWVyeT86IHN0cmluZ1xuICAgIG1heF9xdWVyaWVzPzogbnVtYmVyXG4gIH0pOiBQcm9taXNlPHtcbiAgICBwcmltYXJ5X3F1ZXJ5OiBzdHJpbmdcbiAgICBzdWdnZXN0ZWRfcXVlcmllczogc3RyaW5nW11cbiAgICByZWxhdGVkX3RvcGljczogc3RyaW5nW11cbiAgICBzZWFyY2hfc3RyYXRlZ3k6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICBvcHRpbWl6YXRpb25fbm90ZXM6IHN0cmluZ1tdXG4gICAgZ2VuZXJhdGVkX2F0OiBzdHJpbmdcbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdCgnL3NtYXJ0LWxpdGVyYXR1cmUvZ2VuZXJhdGUtc21hcnQtcXVlcmllcycsIGRhdGEpXG4gIH0sXG5cbiAgLy8gUGVycGxleGl0eemjjuagvOaQnOe0olxuICBhc3luYyBwZXJwbGV4aXR5U2VhcmNoKGRhdGE6IHtcbiAgICBxdWVyeTogc3RyaW5nXG4gICAgcmVxdWlyZW1lbnRzPzogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIGluY2x1ZGVfYW5hbHlzaXM/OiBib29sZWFuXG4gICAgbWF4X3BhcGVycz86IG51bWJlclxuICB9KTogUHJvbWlzZTx7XG4gICAgcXVlcnk6IHN0cmluZ1xuICAgIG9wdGltaXplZF9xdWVyeTogc3RyaW5nXG4gICAgcGFwZXJzOiBhbnlbXVxuICAgIGFpX3N1bW1hcnk6IHN0cmluZ1xuICAgIHJlbGF0ZWRfdG9waWNzOiBzdHJpbmdbXVxuICAgIHNvdXJjZXM6IHN0cmluZ1tdXG4gICAgc2VhcmNoX3RpbWVfbXM6IG51bWJlclxuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5wb3N0KCcvc21hcnQtbGl0ZXJhdHVyZS9wZXJwbGV4aXR5LXNlYXJjaCcsIGRhdGEpXG4gIH0sXG5cbiAgLy8g5aKe5by65paH54yu5pCc57SiXG4gIGFzeW5jIGVuaGFuY2VkTGl0ZXJhdHVyZVNlYXJjaChkYXRhOiB7XG4gICAgcXVlcnk6IHN0cmluZ1xuICAgIHJlcXVpcmVtZW50czogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIHVzZXJfcHJlZmVyZW5jZXM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgZW5hYmxlX2xpdGVyYXR1cmVfc2VhcmNoPzogYm9vbGVhblxuICB9KTogUHJvbWlzZTx7XG4gICAgc21hcnRfcXVlcmllczogYW55XG4gICAgbGl0ZXJhdHVyZV9yZXN1bHRzOiBhbnlbXVxuICAgIG9wdGlvbmFsX2xpdGVyYXR1cmU6IGFueVxuICAgIHRvdGFsX3BhcGVyczogbnVtYmVyXG4gICAgc291cmNlczogc3RyaW5nW11cbiAgICByZWNvbW1lbmRhdGlvbnM6IHN0cmluZ1tdXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9zbWFydC1saXRlcmF0dXJlL2VuaGFuY2VkLWxpdGVyYXR1cmUtc2VhcmNoJywgZGF0YSlcbiAgfVxufVxuXG4vLyDmlrnmoYjnlJ/miJBBUElcbmV4cG9ydCBjb25zdCBzb2x1dGlvbkFwaSA9IHtcbiAgLy8g55Sf5oiQ5a6M5pW05pa55qGIXG4gIGFzeW5jIGdlbmVyYXRlU29sdXRpb24oZGF0YToge1xuICAgIHJlcXVpcmVtZW50czogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIHVzZXJfbWVzc2FnZT86IHN0cmluZ1xuICAgIGluY2x1ZGVfY29zdF9hbmFseXNpcz86IGJvb2xlYW5cbiAgICBpbmNsdWRlX3Jpc2tfYXNzZXNzbWVudD86IGJvb2xlYW5cbiAgfSk6IFByb21pc2U8e1xuICAgIHNvbHV0aW9uX2lkOiBzdHJpbmdcbiAgICBnZW5lcmF0ZWRfYXQ6IHN0cmluZ1xuICAgIGNsaWVudF9yZXF1aXJlbWVudHM6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICByZWNvbW1lbmRlZF9zb2x1dGlvbjogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIGNvc3RfYW5hbHlzaXM6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICByaXNrX2Fzc2Vzc21lbnQ6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICB0aW1lbGluZTogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIGRlbGl2ZXJhYmxlczogc3RyaW5nW11cbiAgICBuZXh0X3N0ZXBzOiBzdHJpbmdbXVxuICAgIGNvbnRhY3RfaW5mbzogUmVjb3JkPHN0cmluZywgYW55PlxuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5wb3N0KCcvc29sdXRpb24vZ2VuZXJhdGUtc29sdXRpb24nLCBkYXRhKVxuICB9LFxuXG4gIC8vIOW/q+mAn+aIkOacrOS8sOeul1xuICBhc3luYyBxdWlja0VzdGltYXRlKHJlcXVpcmVtZW50czogUmVjb3JkPHN0cmluZywgYW55Pik6IFByb21pc2U8e1xuICAgIHF1aWNrX2VzdGltYXRlOiB7XG4gICAgICByZWNvbW1lbmRlZF9wbGF0Zm9ybTogc3RyaW5nXG4gICAgICBlc3RpbWF0ZWRfY29zdDogc3RyaW5nXG4gICAgICBlc3RpbWF0ZWRfdGltZWxpbmU6IHN0cmluZ1xuICAgICAgc2FtcGxlX2NvdW50OiBudW1iZXJcbiAgICAgIGNvc3RfcGVyX3NhbXBsZTogc3RyaW5nXG4gICAgfVxuICAgIG5leHRfc3RlcHM6IHN0cmluZ1tdXG4gICAgZGlzY2xhaW1lcjogc3RyaW5nXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9zb2x1dGlvbi9xdWljay1lc3RpbWF0ZScsIHJlcXVpcmVtZW50cylcbiAgfSxcblxuICAvLyDojrflj5bmlrnmoYjmqKHmnb9cbiAgYXN5bmMgZ2V0U29sdXRpb25UZW1wbGF0ZXMoKTogUHJvbWlzZTx7XG4gICAgYXZhaWxhYmxlX3RlbXBsYXRlczogUmVjb3JkPHN0cmluZywgYW55PlxuICAgIGN1c3RvbWl6YXRpb25fbm90ZTogc3RyaW5nXG4gICAgY29uc3VsdGF0aW9uX2F2YWlsYWJsZTogc3RyaW5nXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LmdldCgnL3NvbHV0aW9uL3NvbHV0aW9uLXRlbXBsYXRlcycpXG4gIH0sXG5cbiAgLy8g6aqM6K+B6ZyA5rGC5a6M5pW05oCnXG4gIGFzeW5jIHZhbGlkYXRlUmVxdWlyZW1lbnRzKHJlcXVpcmVtZW50czogUmVjb3JkPHN0cmluZywgYW55Pik6IFByb21pc2U8e1xuICAgIHZhbGlkYXRpb25fcmVzdWx0OiB7XG4gICAgICBpc192YWxpZDogYm9vbGVhblxuICAgICAgY29tcGxldGVuZXNzX3BlcmNlbnRhZ2U6IG51bWJlclxuICAgICAgbWlzc2luZ19yZXF1aXJlZF9maWVsZHM6IHN0cmluZ1tdXG4gICAgICBjb21wbGV0ZWRfZmllbGRzOiBzdHJpbmdbXVxuICAgICAgcmVjb21tZW5kYXRpb25zOiBzdHJpbmdbXVxuICAgIH1cbiAgICBuZXh0X2FjdGlvbjogc3RyaW5nXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9zb2x1dGlvbi92YWxpZGF0ZS1yZXF1aXJlbWVudHMnLCByZXF1aXJlbWVudHMpXG4gIH0sXG5cbiAgLy8g6I635Y+W5bmz5Y+w5a+55q+UXG4gIGFzeW5jIGdldFBsYXRmb3JtQ29tcGFyaXNvbigpOiBQcm9taXNlPHtcbiAgICBwbGF0Zm9ybV9jb21wYXJpc29uOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgc2VsZWN0aW9uX2d1aWRlOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgY29uc3VsdGF0aW9uX25vdGU6IHN0cmluZ1xuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5nZXQoJy9zb2x1dGlvbi9wbGF0Zm9ybS1jb21wYXJpc29uJylcbiAgfSxcblxuICAvLyDnlJ/miJDnu7zlkIjmlrnmoYjmoYbmnrbvvIjmjInorr7orqHmlofmoaPopoHmsYLvvIlcbiAgYXN5bmMgZ2VuZXJhdGVDb21wcmVoZW5zaXZlRnJhbWV3b3JrKGRhdGE6IHtcbiAgICByZXF1aXJlbWVudHM6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICB1c2VyX21lc3NhZ2U/OiBzdHJpbmdcbiAgICBmcmFtZXdvcmtfdGVtcGxhdGU/OiAnc3RhbmRhcmQnIHwgJ2RldGFpbGVkJyB8ICdzaW1wbGlmaWVkJ1xuICAgIGVuYWJsZV9saXRlcmF0dXJlX3NlYXJjaD86IGJvb2xlYW5cbiAgfSk6IFByb21pc2U8e1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgICBmcmFtZXdvcmtfaWQ6IHN0cmluZ1xuICAgIGRhdGE6IHtcbiAgICAgIHNvbHV0aW9uX292ZXJ2aWV3OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgICByZXNlYXJjaF9pbnRlbnRfYW5hbHlzaXM6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgIGtleV9mYWN0b3JzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgICBsaXRlcmF0dXJlX3JlY29tbWVuZGF0aW9uczogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgcGxhdGZvcm1fY29tcGFyaXNvbjogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgaW1wbGVtZW50YXRpb25fcGxhbjogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgcmlza19hc3Nlc3NtZW50OiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgICBnZW5lcmF0ZWRfYXQ6IHN0cmluZ1xuICAgICAgdGVtcGxhdGVfdXNlZDogc3RyaW5nXG4gICAgfVxuICAgIGdlbmVyYXRpb25fdGltZTogc3RyaW5nXG4gICAgdGVtcGxhdGVfdXNlZDogc3RyaW5nXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9zb2x1dGlvbi9jb21wcmVoZW5zaXZlLWZyYW1ld29yaycsIHtcbiAgICAgIHJlcXVpcmVtZW50czogZGF0YS5yZXF1aXJlbWVudHMsXG4gICAgICB1c2VyX21lc3NhZ2U6IGRhdGEudXNlcl9tZXNzYWdlIHx8ICcnLFxuICAgICAgZnJhbWV3b3JrX3RlbXBsYXRlOiBkYXRhLmZyYW1ld29ya190ZW1wbGF0ZSB8fCAnc3RhbmRhcmQnLFxuICAgICAgZW5hYmxlX2xpdGVyYXR1cmVfc2VhcmNoOiBkYXRhLmVuYWJsZV9saXRlcmF0dXJlX3NlYXJjaCAhPT0gZmFsc2VcbiAgICB9KVxuICB9XG59XG5cbi8vIOaZuuiDveaOqOiNkEFQSVxuZXhwb3J0IGNvbnN0IGludGVsbGlnZW50UmVjb21tZW5kYXRpb25BcGkgPSB7XG4gIC8vIOiOt+WPlue7vOWQiOaZuuiDveaOqOiNkFxuICBhc3luYyBnZXRDb21wcmVoZW5zaXZlUmVjb21tZW5kYXRpb24oZGF0YToge1xuICAgIHNwZWNpZXNUeXBlOiBzdHJpbmdcbiAgICBleHBlcmltZW50VHlwZTogc3RyaW5nXG4gICAgcmVzZWFyY2hHb2FsOiBzdHJpbmdcbiAgICBzYW1wbGVUeXBlOiBzdHJpbmdcbiAgICBzYW1wbGVDb3VudDogc3RyaW5nXG4gICAgc2FtcGxlU3RhdHVzOiBzdHJpbmdcbiAgICBwcm9jZXNzaW5nTWV0aG9kOiBzdHJpbmdcbiAgICBjZWxsQ291bnQ6IHN0cmluZ1xuICAgIGNlbGxWaWFiaWxpdHk6IHN0cmluZ1xuICAgIGJ1ZGdldFJhbmdlOiBzdHJpbmdcbiAgICBwcm9qZWN0RHVyYXRpb246IHN0cmluZ1xuICAgIHVyZ2VuY3k6IHN0cmluZ1xuICAgIHNlcXVlbmNpbmdEZXB0aDogc3RyaW5nXG4gICAgYW5hbHlzaXNUeXBlOiBzdHJpbmdcbiAgICBkYXRhQW5hbHlzaXNOZWVkczogc3RyaW5nXG4gICAgY2VsbFNvcnRpbmc6IHN0cmluZ1xuICAgIGFkZGl0aW9uYWxSZXF1aXJlbWVudHM/OiBzdHJpbmdcbiAgfSk6IFByb21pc2U8e1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgICBkYXRhOiB7XG4gICAgICBzZWFyY2hfcXVlcmllczoge1xuICAgICAgICBwcmltYXJ5X3F1ZXJ5OiBzdHJpbmdcbiAgICAgICAgc3VnZ2VzdGVkX3F1ZXJpZXM6IHN0cmluZ1tdXG4gICAgICAgIHJlbGF0ZWRfdG9waWNzOiBzdHJpbmdbXVxuICAgICAgICBzZWFyY2hfc3RyYXRlZ3k6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgb3B0aW1pemF0aW9uX25vdGVzOiBzdHJpbmdbXVxuICAgICAgfVxuICAgICAgbGl0ZXJhdHVyZV9yZXN1bHRzOiB7XG4gICAgICAgIGxvY2FsX3Jlc3VsdHM6IGFueVtdXG4gICAgICAgIGV4dGVybmFsX3Jlc3VsdHM6IGFueVtdXG4gICAgICAgIGNvbWJpbmVkX3Jlc3VsdHM6IGFueVtdXG4gICAgICB9XG4gICAgICBleHBhbmRlZF9rZXl3b3Jkczoge1xuICAgICAgICBzZW1hbnRpY19leHBhbnNpb246IHN0cmluZ1tdXG4gICAgICAgIGNyb3NzX2Rpc2NpcGxpbmFyeTogc3RyaW5nW11cbiAgICAgICAgdHJlbmRpbmdfdGVybXM6IHN0cmluZ1tdXG4gICAgICAgIG1vbGVjdWxhcl90YXJnZXRzOiBzdHJpbmdbXVxuICAgICAgICBjbGluaWNhbF90ZXJtczogc3RyaW5nW11cbiAgICAgIH1cbiAgICAgIGhvdF9wYXBlcnM6IEFycmF5PHtcbiAgICAgICAgdGl0bGU6IHN0cmluZ1xuICAgICAgICBhdXRob3JzOiBzdHJpbmdcbiAgICAgICAgam91cm5hbDogc3RyaW5nXG4gICAgICAgIGltcGFjdF9mYWN0b3I6IG51bWJlclxuICAgICAgICBwdWJsaWNhdGlvbl9kYXRlOiBzdHJpbmdcbiAgICAgICAgY2l0YXRpb25fY291bnQ6IG51bWJlclxuICAgICAgICB0cmVuZF9zY29yZTogbnVtYmVyXG4gICAgICAgIHJlYXNvbjogc3RyaW5nXG4gICAgICAgIGRvaTogc3RyaW5nXG4gICAgICAgIHJlbGV2YW5jZTogbnVtYmVyXG4gICAgICB9PlxuICAgICAgdGVjaF9yZWNvbW1lbmRhdGlvbnM6IEFycmF5PHtcbiAgICAgICAgcGxhdGZvcm06IHN0cmluZ1xuICAgICAgICBwbGF0Zm9ybV9pZDogc3RyaW5nXG4gICAgICAgIHNwZWNpZmljYXRpb25zOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgICAgIGNvc3RfYnJlYWtkb3duOiBSZWNvcmQ8c3RyaW5nLCBhbnk+XG4gICAgICAgIHRvdGFsX2VzdGltYXRlZF9jb3N0OiBudW1iZXJcbiAgICAgICAgYnVkZ2V0X21hdGNoX3Njb3JlOiBudW1iZXJcbiAgICAgICAgdGltZWxpbmVfZXN0aW1hdGU6IFJlY29yZDxzdHJpbmcsIHN0cmluZz5cbiAgICAgICAgYWR2YW50YWdlczogc3RyaW5nW11cbiAgICAgICAgY29uc2lkZXJhdGlvbnM6IHN0cmluZ1tdXG4gICAgICAgIHN1aXRhYmlsaXR5X3Njb3JlOiBudW1iZXJcbiAgICAgIH0+XG4gICAgICBwcm9qZWN0X3NvbHV0aW9uOiB7XG4gICAgICAgIG92ZXJ2aWV3OiBzdHJpbmdcbiAgICAgICAgZXhwZXJpbWVudGFsX2Rlc2lnbjogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgICBkYXRhX2FuYWx5c2lzX3BsYW46IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgZXhwZWN0ZWRfb3V0Y29tZXM6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgcHVibGljYXRpb25fc3RyYXRlZ3k6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgY29sbGFib3JhdGlvbl9vcHBvcnR1bml0aWVzOiBBcnJheTx7XG4gICAgICAgICAgaW5zdGl0dXRpb246IHN0cmluZ1xuICAgICAgICAgIGNvbnRhY3Q6IHN0cmluZ1xuICAgICAgICAgIGV4cGVydGlzZTogc3RyaW5nXG4gICAgICAgICAgY29sbGFib3JhdGlvbl90eXBlOiBzdHJpbmdcbiAgICAgICAgfT5cbiAgICAgICAgdXBncmFkZV9wYXRod2F5czogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgfVxuICAgICAgcmlza19hc3Nlc3NtZW50OiB7XG4gICAgICAgIHRlY2huaWNhbF9yaXNrczogQXJyYXk8e1xuICAgICAgICAgIHJpc2s6IHN0cmluZ1xuICAgICAgICAgIHByb2JhYmlsaXR5OiBzdHJpbmdcbiAgICAgICAgICBpbXBhY3Q6IHN0cmluZ1xuICAgICAgICAgIG1pdGlnYXRpb246IHN0cmluZ1xuICAgICAgICB9PlxuICAgICAgICB0aW1lbGluZV9yaXNrczogYW55W11cbiAgICAgICAgYnVkZ2V0X3Jpc2tzOiBhbnlbXVxuICAgICAgICBkYXRhX3Jpc2tzOiBhbnlbXVxuICAgICAgICBvdmVyYWxsX3Jpc2tfbGV2ZWw6IHN0cmluZ1xuICAgICAgICBzdWNjZXNzX3Byb2JhYmlsaXR5OiBzdHJpbmdcbiAgICAgIH1cbiAgICAgIGdlbmVyYXRlZF9hdDogc3RyaW5nXG4gICAgfVxuICAgIHByb2Nlc3NpbmdfdGltZT86IG51bWJlclxuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5wb3N0KCcvaW50ZWxsaWdlbnQtcmVjb21tZW5kYXRpb24vY29tcHJlaGVuc2l2ZS1yZWNvbW1lbmRhdGlvbicsIGRhdGEpXG4gIH0sXG5cbiAgLy8g6I635Y+W5pm66IO95YWz6ZSu6K+N5omp5bGVXG4gIGFzeW5jIGdldEtleXdvcmRFeHBhbnNpb24oZGF0YTogYW55KTogUHJvbWlzZTx7XG4gICAgc3VjY2VzczogYm9vbGVhblxuICAgIGRhdGE6IHtcbiAgICAgIGV4cGFuZGVkX2tleXdvcmRzOiB7XG4gICAgICAgIHNlbWFudGljX2V4cGFuc2lvbjogc3RyaW5nW11cbiAgICAgICAgY3Jvc3NfZGlzY2lwbGluYXJ5OiBzdHJpbmdbXVxuICAgICAgICB0cmVuZGluZ190ZXJtczogc3RyaW5nW11cbiAgICAgICAgbW9sZWN1bGFyX3RhcmdldHM6IHN0cmluZ1tdXG4gICAgICAgIGNsaW5pY2FsX3Rlcm1zOiBzdHJpbmdbXVxuICAgICAgfVxuICAgICAgZ2VuZXJhdGVkX2F0OiBzdHJpbmdcbiAgICB9XG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LnBvc3QoJy9pbnRlbGxpZ2VudC1yZWNvbW1lbmRhdGlvbi9rZXl3b3JkLWV4cGFuc2lvbicsIGRhdGEpXG4gIH0sXG5cbiAgLy8g6I635Y+W54Ot54K55paH54yu5o6o6I2QXG4gIGFzeW5jIGdldEhvdFBhcGVycyhkYXRhOiBhbnkpOiBQcm9taXNlPHtcbiAgICBzdWNjZXNzOiBib29sZWFuXG4gICAgZGF0YToge1xuICAgICAgaG90X3BhcGVyczogQXJyYXk8e1xuICAgICAgICB0aXRsZTogc3RyaW5nXG4gICAgICAgIGF1dGhvcnM6IHN0cmluZ1xuICAgICAgICBqb3VybmFsOiBzdHJpbmdcbiAgICAgICAgaW1wYWN0X2ZhY3RvcjogbnVtYmVyXG4gICAgICAgIHB1YmxpY2F0aW9uX2RhdGU6IHN0cmluZ1xuICAgICAgICBjaXRhdGlvbl9jb3VudDogbnVtYmVyXG4gICAgICAgIHRyZW5kX3Njb3JlOiBudW1iZXJcbiAgICAgICAgcmVhc29uOiBzdHJpbmdcbiAgICAgICAgZG9pOiBzdHJpbmdcbiAgICAgICAgcmVsZXZhbmNlOiBudW1iZXJcbiAgICAgIH0+XG4gICAgICB0b3RhbF9jb3VudDogbnVtYmVyXG4gICAgfVxuICB9PiB7XG4gICAgcmV0dXJuIGFwaUNsaWVudC5wb3N0KCcvaW50ZWxsaWdlbnQtcmVjb21tZW5kYXRpb24vaG90LXBhcGVycycsIGRhdGEpXG4gIH0sXG5cbiAgLy8g6I635Y+W5oqA5pyv5bmz5Y+w5o6o6I2QXG4gIGFzeW5jIGdldFRlY2hSZWNvbW1lbmRhdGlvbnMoZGF0YTogYW55KTogUHJvbWlzZTx7XG4gICAgc3VjY2VzczogYm9vbGVhblxuICAgIGRhdGE6IHtcbiAgICAgIHRlY2hfcmVjb21tZW5kYXRpb25zOiBBcnJheTx7XG4gICAgICAgIHBsYXRmb3JtOiBzdHJpbmdcbiAgICAgICAgcGxhdGZvcm1faWQ6IHN0cmluZ1xuICAgICAgICBzcGVjaWZpY2F0aW9uczogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgICBjb3N0X2JyZWFrZG93bjogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgICB0b3RhbF9lc3RpbWF0ZWRfY29zdDogbnVtYmVyXG4gICAgICAgIGJ1ZGdldF9tYXRjaF9zY29yZTogbnVtYmVyXG4gICAgICAgIHRpbWVsaW5lX2VzdGltYXRlOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+XG4gICAgICAgIGFkdmFudGFnZXM6IHN0cmluZ1tdXG4gICAgICAgIGNvbnNpZGVyYXRpb25zOiBzdHJpbmdbXVxuICAgICAgICBzdWl0YWJpbGl0eV9zY29yZTogbnVtYmVyXG4gICAgICB9PlxuICAgICAgdG90YWxfY291bnQ6IG51bWJlclxuICAgIH1cbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdCgnL2ludGVsbGlnZW50LXJlY29tbWVuZGF0aW9uL3RlY2gtcmVjb21tZW5kYXRpb25zJywgZGF0YSlcbiAgfSxcblxuICAvLyDojrflj5bpobnnm67mlbTkvZPop6PlhrPmlrnmoYhcbiAgYXN5bmMgZ2V0UHJvamVjdFNvbHV0aW9uKGRhdGE6IGFueSk6IFByb21pc2U8e1xuICAgIHN1Y2Nlc3M6IGJvb2xlYW5cbiAgICBkYXRhOiB7XG4gICAgICBwcm9qZWN0X3NvbHV0aW9uOiB7XG4gICAgICAgIG92ZXJ2aWV3OiBzdHJpbmdcbiAgICAgICAgZXhwZXJpbWVudGFsX2Rlc2lnbjogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgICBkYXRhX2FuYWx5c2lzX3BsYW46IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgZXhwZWN0ZWRfb3V0Y29tZXM6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgcHVibGljYXRpb25fc3RyYXRlZ3k6IFJlY29yZDxzdHJpbmcsIGFueT5cbiAgICAgICAgY29sbGFib3JhdGlvbl9vcHBvcnR1bml0aWVzOiBBcnJheTx7XG4gICAgICAgICAgaW5zdGl0dXRpb246IHN0cmluZ1xuICAgICAgICAgIGNvbnRhY3Q6IHN0cmluZ1xuICAgICAgICAgIGV4cGVydGlzZTogc3RyaW5nXG4gICAgICAgICAgY29sbGFib3JhdGlvbl90eXBlOiBzdHJpbmdcbiAgICAgICAgfT5cbiAgICAgICAgdXBncmFkZV9wYXRod2F5czogUmVjb3JkPHN0cmluZywgYW55PlxuICAgICAgfVxuICAgIH1cbiAgfT4ge1xuICAgIHJldHVybiBhcGlDbGllbnQucG9zdCgnL2ludGVsbGlnZW50LXJlY29tbWVuZGF0aW9uL3Byb2plY3Qtc29sdXRpb24nLCBkYXRhKVxuICB9LFxuXG4gIC8vIOWBpeW6t+ajgOafpVxuICBhc3luYyBoZWFsdGhDaGVjaygpOiBQcm9taXNlPHtcbiAgICBzdGF0dXM6IHN0cmluZ1xuICAgIHNlcnZpY2U6IHN0cmluZ1xuICAgIHRpbWVzdGFtcDogc3RyaW5nXG4gIH0+IHtcbiAgICByZXR1cm4gYXBpQ2xpZW50LmdldCgnL2ludGVsbGlnZW50LXJlY29tbWVuZGF0aW9uL2hlYWx0aCcpXG4gIH1cbn1cblxuLy8g5a+85Ye6VG9rZW7nrqHnkIblmajlkoxBUEnlrqLmiLfnq69cbmV4cG9ydCB7IFRva2VuTWFuYWdlciwgYXBpQ2xpZW50IH1cbiJdLCJuYW1lcyI6WyJnZXRBcGlCYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJwcm90b2NvbCIsIndpbmRvdyIsImxvY2F0aW9uIiwiaG9zdG5hbWUiLCJBUElfQkFTRV9VUkwiLCJUb2tlbk1hbmFnZXIiLCJUT0tFTl9LRVkiLCJSRUZSRVNIX0tFWSIsImdldFRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInNldFRva2VuIiwidG9rZW4iLCJzZXRJdGVtIiwicmVtb3ZlVG9rZW4iLCJyZW1vdmVJdGVtIiwiaXNUb2tlbkV4cGlyZWQiLCJwYXlsb2FkIiwiSlNPTiIsInBhcnNlIiwiYXRvYiIsInNwbGl0IiwiZXhwIiwiRGF0ZSIsIm5vdyIsIkFwaUNsaWVudCIsImNvbnN0cnVjdG9yIiwiYmFzZVVSTCIsInJlcXVlc3QiLCJlbmRwb2ludCIsIm9wdGlvbnMiLCJ1cmwiLCJoZWFkZXJzIiwiQXV0aG9yaXphdGlvbiIsImNvbmZpZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJjYXRjaCIsImVycm9yTWVzc2FnZSIsImRldGFpbCIsIm1lc3NhZ2UiLCJzdGF0dXMiLCJzdGF0dXNUZXh0IiwiRXJyb3IiLCJlcnJvciIsIlR5cGVFcnJvciIsImluY2x1ZGVzIiwiY29uc29sZSIsImdldCIsIm1ldGhvZCIsInBvc3QiLCJkYXRhIiwiYm9keSIsInN0cmluZ2lmeSIsInVuZGVmaW5lZCIsInB1dCIsImRlbGV0ZSIsImFwaUNsaWVudCIsImF1dGhBcGkiLCJsb2dpbiIsImNyZWRlbnRpYWxzIiwiYWNjZXNzX3Rva2VuIiwicmVnaXN0ZXIiLCJ1c2VyRGF0YSIsImdldEN1cnJlbnRVc2VyIiwicmVmcmVzaFRva2VuIiwiY2hhbmdlUGFzc3dvcmQiLCJsb2dvdXQiLCJjb252ZXJzYXRpb25BcGkiLCJzZW5kTWVzc2FnZSIsImdldEhpc3RvcnkiLCJwYWdlIiwic2l6ZSIsImxpdGVyYXR1cmVBcGkiLCJzZWFyY2hMaXRlcmF0dXJlIiwiZ2V0TGl0ZXJhdHVyZVJlY29tbWVuZGF0aW9ucyIsImdldENhdGVnb3JpZXMiLCJnZXRUcmVuZGluZ0xpdGVyYXR1cmUiLCJsaW1pdCIsImdldFN0YXRzIiwiZ2V0U2VhcmNoU3RhdHVzIiwiY29sbGVjdEZvclJlcXVpcmVtZW50cyIsImN1c3RvbWVyUHJvZmlsZUFwaSIsImdldFByb2ZpbGUiLCJ1c2VySWQiLCJhbmFseXplUHJvZmlsZSIsImdldEluc2lnaHRzIiwiZ2V0UmVjb21tZW5kYXRpb25zIiwidXBkYXRlUHJvZmlsZSIsInByb2ZpbGVEYXRhIiwidXBkYXRlRnJvbVJlcXVpcmVtZW50cyIsInJlcXVpcmVtZW50RGF0YSIsImFuYWx5emVXZWJQcm9maWxlIiwiYW5hbHl6ZVRleHRQcm9maWxlIiwiZ2V0QW5hbHlzaXNIaXN0b3J5Iiwic21hcnRMaXRlcmF0dXJlQXBpIiwiZ2VuZXJhdGVTbWFydFF1ZXJpZXMiLCJwZXJwbGV4aXR5U2VhcmNoIiwiZW5oYW5jZWRMaXRlcmF0dXJlU2VhcmNoIiwic29sdXRpb25BcGkiLCJnZW5lcmF0ZVNvbHV0aW9uIiwicXVpY2tFc3RpbWF0ZSIsInJlcXVpcmVtZW50cyIsImdldFNvbHV0aW9uVGVtcGxhdGVzIiwidmFsaWRhdGVSZXF1aXJlbWVudHMiLCJnZXRQbGF0Zm9ybUNvbXBhcmlzb24iLCJnZW5lcmF0ZUNvbXByZWhlbnNpdmVGcmFtZXdvcmsiLCJ1c2VyX21lc3NhZ2UiLCJmcmFtZXdvcmtfdGVtcGxhdGUiLCJlbmFibGVfbGl0ZXJhdHVyZV9zZWFyY2giLCJpbnRlbGxpZ2VudFJlY29tbWVuZGF0aW9uQXBpIiwiZ2V0Q29tcHJlaGVuc2l2ZVJlY29tbWVuZGF0aW9uIiwiZ2V0S2V5d29yZEV4cGFuc2lvbiIsImdldEhvdFBhcGVycyIsImdldFRlY2hSZWNvbW1lbmRhdGlvbnMiLCJnZXRQcm9qZWN0U29sdXRpb24iLCJoZWFsdGhDaGVjayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n/**\n * 权限检查工具函数\n */ // 权限定义\nconst PERMISSIONS = {\n    // 仪表盘权限\n    view_dashboard: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    export_dashboard: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    customize_dashboard: [\n        \"super_admin\"\n    ],\n    // 客户管理权限\n    view_customers: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    manage_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    add_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    edit_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    delete_customers: [\n        \"super_admin\"\n    ],\n    export_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    // 方案管理权限\n    view_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\",\n        \"customer\"\n    ],\n    create_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    edit_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    delete_solutions: [\n        \"super_admin\"\n    ],\n    share_solutions: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    // 知识库权限\n    view_knowledge: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\",\n        \"customer\"\n    ],\n    manage_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    add_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    edit_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    delete_knowledge: [\n        \"super_admin\"\n    ],\n    approve_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    // 系统管理权限\n    manage_users: [\n        \"super_admin\"\n    ],\n    manage_permissions: [\n        \"super_admin\"\n    ],\n    manage_system: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    view_analytics: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ]\n};\n/**\n * 检查用户是否具有指定权限\n * @param permission 权限名称\n * @param userRole 用户角色\n * @returns 是否具有权限\n */ function hasPermission(permission, userRole) {\n    if (!userRole) return false;\n    const allowedRoles = PERMISSIONS[permission];\n    return allowedRoles.includes(userRole);\n}\n/**\n * 获取用户的所有权限\n * @param userRole 用户角色\n * @returns 权限列表\n */ function getUserPermissions(userRole) {\n    return Object.keys(PERMISSIONS).filter((permission)=>hasPermission(permission, userRole));\n}\n/**\n * 检查用户是否具有任一权限\n * @param permissions 权限列表\n * @param userRole 用户角色\n * @returns 是否具有任一权限\n */ function hasAnyPermission(permissions, userRole) {\n    return permissions.some((permission)=>hasPermission(permission, userRole));\n}\n/**\n * 检查用户是否具有所有权限\n * @param permissions 权限列表\n * @param userRole 用户角色\n * @returns 是否具有所有权限\n */ function hasAllPermissions(permissions, userRole) {\n    return permissions.every((permission)=>hasPermission(permission, userRole));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNjYWlcXERlc2t0b3BcXERldlxcQ2VsbEZvcmdlIEFJXFxmcm9udGVuZFxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(ssr)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2NjYWklNUMlNUNEZXNrdG9wJTVDJTVDRGV2JTVDJTVDQ2VsbEZvcmdlJTIwQUklNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFnSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcY2NhaVxcXFxEZXNrdG9wXFxcXERldlxcXFxDZWxsRm9yZ2UgQUlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/zod","vendor-chunks/@hookform","vendor-chunks/react-hook-form"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cccai%5CDesktop%5CDev%5CCellForge%20AI%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();