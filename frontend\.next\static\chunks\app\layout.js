/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2d874f610180\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmQ4NzRmNjEwMTgwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU4QjtBQUlWO0FBRWIsU0FBU0MsY0FBYyxLQUEwQztRQUExQyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkIsR0FBMUM7SUFDNUIscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDO0tBRmdCRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjY2FpXFxEZXNrdG9wXFxEZXZcXENlbGxGb3JnZSBBSVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXHRoZW1lLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcixcbiAgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMsXG59IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/theme-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst defaultPermissions = [\n    {\n        id: \"view_dashboard\",\n        name: \"查看仪表盘\",\n        description: \"允许用户查看数据分析仪表盘\",\n        roles: [\n            \"super_admin\",\n            \"sales\"\n        ]\n    },\n    {\n        id: \"manage_customers\",\n        name: \"管理客户\",\n        description: \"允许用户添加、编辑和删除客户信息\",\n        roles: [\n            \"super_admin\",\n            \"sales\"\n        ]\n    },\n    {\n        id: \"view_customers\",\n        name: \"查看客户\",\n        description: \"允许用户查看客户信息\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"manage_knowledge\",\n        name: \"管理知识库\",\n        description: \"允许用户添加、编辑和删除知识库内容\",\n        roles: [\n            \"super_admin\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"view_knowledge\",\n        name: \"查看知识库\",\n        description: \"允许用户查看知识库内容\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\",\n            \"customer\"\n        ]\n    },\n    {\n        id: \"manage_solutions\",\n        name: \"管理解决方案\",\n        description: \"允许用户创建和编辑解决方案\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\"\n        ]\n    },\n    {\n        id: \"view_solutions\",\n        name: \"查看解决方案\",\n        description: \"允许用户查看解决方案\",\n        roles: [\n            \"super_admin\",\n            \"sales\",\n            \"operations\",\n            \"customer\"\n        ]\n    },\n    {\n        id: \"manage_users\",\n        name: \"管理用户\",\n        description: \"允许用户添加、编辑和删除用户账户\",\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    {\n        id: \"manage_permissions\",\n        name: \"管理权限\",\n        description: \"允许用户配置系统权限\",\n        roles: [\n            \"super_admin\"\n        ]\n    }\n];\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 辅助函数：将API用户转换为前端用户格式\nfunction transformApiUser(apiUser) {\n    // 构建用户显示名称\n    let displayName = apiUser.username; // 默认使用用户名\n    if (apiUser.first_name && apiUser.last_name) {\n        displayName = \"\".concat(apiUser.first_name, \" \").concat(apiUser.last_name);\n    } else if (apiUser.first_name) {\n        displayName = apiUser.first_name;\n    } else if (apiUser.last_name) {\n        displayName = apiUser.last_name;\n    }\n    return {\n        ...apiUser,\n        id: apiUser.id.toString(),\n        name: displayName,\n        avatar: \"/placeholder.svg?height=40&width=40&query=avatar\"\n    };\n}\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultPermissions);\n    // 从API加载用户信息\n    const loadUser = async ()=>{\n        try {\n            // 检查是否有有效的token\n            const token = localStorage.getItem('cellforge_access_token');\n            if (!token) {\n                setUser(null);\n                return;\n            }\n            const apiUser = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.getCurrentUser();\n            const transformedUser = transformApiUser(apiUser);\n            setUser(transformedUser);\n        } catch (error) {\n            console.error(\"Failed to load user:\", error);\n            // 如果获取用户失败，清除可能过期的token\n            _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // 只在客户端执行\n                        if (true) {\n                            // 检查是否有token，如果有则尝试加载用户信息\n                            const token = localStorage.getItem('cellforge_access_token');\n                            if (token) {\n                                // 检查token是否过期\n                                try {\n                                    const payload = JSON.parse(atob(token.split('.')[1]));\n                                    if (payload.exp * 1000 > Date.now()) {\n                                        await loadUser();\n                                    } else {\n                                        // token已过期，清除\n                                        _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n                                        setUser(null);\n                                    }\n                                } catch (tokenError) {\n                                    // token格式错误，清除\n                                    _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n                                    setUser(null);\n                                }\n                            } else {\n                                setUser(null);\n                            }\n                        } else {}\n                    } catch (error) {\n                        console.error(\"Auth initialization failed:\", error);\n                        setUser(null);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const hasPermission = (permission)=>{\n        return (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(permission, user === null || user === void 0 ? void 0 : user.role);\n    };\n    const login = async (email, password)=>{\n        setIsLoading(true);\n        try {\n            // 调用登录API\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.login({\n                email,\n                password\n            });\n            // 登录成功后获取用户信息\n            await loadUser();\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.register(userData);\n        // 注册成功，但不自动登录，需要等待管理员审核\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const refreshUser = async ()=>{\n        await loadUser();\n    };\n    const logout = ()=>{\n        _lib_api__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n        setUser(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            permissions,\n            hasPermission,\n            login,\n            register,\n            logout,\n            refreshUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"gJ8ajc94vqzkEISglddBMXkH2sw=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   conversationApi: () => (/* binding */ conversationApi),\n/* harmony export */   customerProfileApi: () => (/* binding */ customerProfileApi),\n/* harmony export */   intelligentRecommendationApi: () => (/* binding */ intelligentRecommendationApi),\n/* harmony export */   literatureApi: () => (/* binding */ literatureApi),\n/* harmony export */   smartLiteratureApi: () => (/* binding */ smartLiteratureApi),\n/* harmony export */   solutionApi: () => (/* binding */ solutionApi)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * API调用工具函数\n */ // 动态获取API基础URL\nconst getApiBaseUrl = ()=>{\n    // 优先使用环境变量\n    if (process.env.NEXT_PUBLIC_API_URL) {\n        return process.env.NEXT_PUBLIC_API_URL;\n    }\n    // 在浏览器环境中，使用当前主机的IP和端口8000\n    if (true) {\n        const protocol = window.location.protocol;\n        const hostname = window.location.hostname;\n        return \"\".concat(protocol, \"//\").concat(hostname, \":8000/api/v1\");\n    }\n    // 服务端渲染时的默认值\n    return 'http://localhost:8000/api/v1';\n};\nconst API_BASE_URL = getApiBaseUrl();\n// Token管理\nclass TokenManager {\n    static getToken() {\n        if (false) {}\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static setToken(token) {\n        if (false) {}\n        localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    static removeToken() {\n        if (false) {}\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_KEY);\n    }\n    static isTokenExpired(token) {\n        try {\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.exp * 1000 < Date.now();\n        } catch (e) {\n            return true;\n        }\n    }\n}\nTokenManager.TOKEN_KEY = 'cellforge_access_token';\nTokenManager.REFRESH_KEY = 'cellforge_refresh_token';\n// HTTP客户端类\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const token = TokenManager.getToken();\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        // 添加认证头\n        if (token && !TokenManager.isTokenExpired(token)) {\n            headers.Authorization = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                // 根据HTTP状态码提供更详细的错误信息\n                let errorMessage = errorData.detail || errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n                if (response.status === 401) {\n                    errorMessage = errorData.detail || \"邮箱或密码错误\";\n                } else if (response.status === 403) {\n                    errorMessage = errorData.detail || \"账户已被暂停或无权限访问\";\n                } else if (response.status === 404) {\n                    errorMessage = \"请求的资源不存在\";\n                } else if (response.status === 422) {\n                    errorMessage = \"请求数据格式错误\";\n                } else if (response.status >= 500) {\n                    errorMessage = \"服务器内部错误，请稍后重试\";\n                }\n                throw new Error(errorMessage);\n            }\n            return await response.json();\n        } catch (error) {\n            // 网络错误处理\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                throw new Error('网络连接失败，请检查网络连接');\n            }\n            console.error('API请求失败:', error);\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n    constructor(baseURL = API_BASE_URL){\n        this.baseURL = baseURL;\n    }\n}\n// 创建API客户端实例\nconst apiClient = new ApiClient();\n// 认证API\nconst authApi = {\n    // 用户登录\n    async login (credentials) {\n        const response = await apiClient.post('/auth/login', credentials);\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 用户注册\n    async register (userData) {\n        return apiClient.post('/auth/register', userData);\n    },\n    // 获取当前用户信息\n    async getCurrentUser () {\n        return apiClient.get('/auth/me');\n    },\n    // 刷新令牌\n    async refreshToken () {\n        const response = await apiClient.post('/auth/refresh');\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 修改密码\n    async changePassword (data) {\n        return apiClient.post('/auth/change-password', data);\n    },\n    // 登出\n    logout () {\n        TokenManager.removeToken();\n    }\n};\n// 对话API\nconst conversationApi = {\n    // 发送消息\n    async sendMessage (data) {\n        return apiClient.post('/conversation/chat', data);\n    },\n    // 获取对话历史\n    async getHistory () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, size = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        return apiClient.get(\"/conversation/history?page=\".concat(page, \"&size=\").concat(size));\n    }\n};\n// 文献API\nconst literatureApi = {\n    // 搜索文献\n    async searchLiterature (data) {\n        return apiClient.post('/literature/search', data);\n    },\n    // 获取文献推荐\n    async getLiteratureRecommendations (data) {\n        return apiClient.post('/literature/recommendations', data);\n    },\n    // 获取文献分类\n    async getCategories () {\n        return apiClient.get('/literature/categories');\n    },\n    // 获取热门文献\n    async getTrendingLiterature () {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiClient.get(\"/literature/trending?limit=\".concat(limit));\n    },\n    // 获取文献统计\n    async getStats () {\n        return apiClient.get('/literature/stats');\n    },\n    // 获取文献搜索状态\n    async getSearchStatus () {\n        return apiClient.get('/literature/search-status');\n    },\n    // 基于需求搜集文献\n    async collectForRequirements (data) {\n        return apiClient.post('/literature/collect-for-requirements', data);\n    }\n};\n// 客户画像API\nconst customerProfileApi = {\n    // 获取客户画像\n    async getProfile (userId) {\n        return apiClient.get(\"/customer/profile/\".concat(userId));\n    },\n    // 分析客户画像\n    async analyzeProfile (data) {\n        return apiClient.post('/customer/analyze', data);\n    },\n    // 获取客户洞察\n    async getInsights (userId) {\n        return apiClient.get(\"/customer/insights/\".concat(userId));\n    },\n    // 获取个性化推荐\n    async getRecommendations (userId) {\n        return apiClient.get(\"/customer/recommendations/\".concat(userId));\n    },\n    // 更新客户画像\n    async updateProfile (userId, profileData) {\n        return apiClient.put(\"/customer/profile/\".concat(userId), profileData);\n    },\n    // 基于需求更新画像\n    async updateFromRequirements (userId, requirementData) {\n        return apiClient.post(\"/customer/requirements/\".concat(userId), requirementData);\n    },\n    // 网页分析\n    async analyzeWebProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-url', data);\n    },\n    // 文本分析\n    async analyzeTextProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-text', data);\n    },\n    // 获取分析历史\n    async getAnalysisHistory () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, size = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        return apiClient.get(\"/profile-analysis/analysis-history?page=\".concat(page, \"&size=\").concat(size));\n    }\n};\n// 智能文献搜索API\nconst smartLiteratureApi = {\n    // 生成智能搜索查询\n    async generateSmartQueries (data) {\n        return apiClient.post('/smart-literature/generate-smart-queries', data);\n    },\n    // Perplexity风格搜索\n    async perplexitySearch (data) {\n        return apiClient.post('/smart-literature/perplexity-search', data);\n    },\n    // 增强文献搜索\n    async enhancedLiteratureSearch (data) {\n        return apiClient.post('/smart-literature/enhanced-literature-search', data);\n    }\n};\n// 方案生成API\nconst solutionApi = {\n    // 生成完整方案\n    async generateSolution (data) {\n        return apiClient.post('/solution/generate-solution', data);\n    },\n    // 快速成本估算\n    async quickEstimate (requirements) {\n        return apiClient.post('/solution/quick-estimate', requirements);\n    },\n    // 获取方案模板\n    async getSolutionTemplates () {\n        return apiClient.get('/solution/solution-templates');\n    },\n    // 验证需求完整性\n    async validateRequirements (requirements) {\n        return apiClient.post('/solution/validate-requirements', requirements);\n    },\n    // 获取平台对比\n    async getPlatformComparison () {\n        return apiClient.get('/solution/platform-comparison');\n    },\n    // 生成综合方案框架（按设计文档要求）\n    async generateComprehensiveFramework (data) {\n        return apiClient.post('/comprehensive-solution/comprehensive-framework', {\n            requirements: data.requirements,\n            user_message: data.user_message || '',\n            framework_template: data.framework_template || 'standard',\n            enable_literature_search: data.enable_literature_search !== false\n        });\n    }\n};\n// 智能推荐API\nconst intelligentRecommendationApi = {\n    // 获取综合智能推荐\n    async getComprehensiveRecommendation (data) {\n        return apiClient.post('/intelligent-recommendation/comprehensive-recommendation', data);\n    },\n    // 获取智能关键词扩展\n    async getKeywordExpansion (data) {\n        return apiClient.post('/intelligent-recommendation/keyword-expansion', data);\n    },\n    // 获取热点文献推荐\n    async getHotPapers (data) {\n        return apiClient.post('/intelligent-recommendation/hot-papers', data);\n    },\n    // 获取技术平台推荐\n    async getTechRecommendations (data) {\n        return apiClient.post('/intelligent-recommendation/tech-recommendations', data);\n    },\n    // 获取项目整体解决方案\n    async getProjectSolution (data) {\n        return apiClient.post('/intelligent-recommendation/project-solution', data);\n    },\n    // 健康检查\n    async healthCheck () {\n        return apiClient.get('/intelligent-recommendation/health');\n    }\n};\n// 导出Token管理器和API客户端\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission)\n/* harmony export */ });\n/**\n * 权限检查工具函数\n */ // 权限定义\nconst PERMISSIONS = {\n    // 仪表盘权限\n    view_dashboard: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    export_dashboard: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    customize_dashboard: [\n        \"super_admin\"\n    ],\n    // 客户管理权限\n    view_customers: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    manage_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    add_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    edit_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    delete_customers: [\n        \"super_admin\"\n    ],\n    export_customers: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    // 方案管理权限\n    view_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\",\n        \"customer\"\n    ],\n    create_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    edit_solutions: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ],\n    delete_solutions: [\n        \"super_admin\"\n    ],\n    share_solutions: [\n        \"super_admin\",\n        \"sales\"\n    ],\n    // 知识库权限\n    view_knowledge: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\",\n        \"customer\"\n    ],\n    manage_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    add_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    edit_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    delete_knowledge: [\n        \"super_admin\"\n    ],\n    approve_knowledge: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    // 系统管理权限\n    manage_users: [\n        \"super_admin\"\n    ],\n    manage_permissions: [\n        \"super_admin\"\n    ],\n    manage_system: [\n        \"super_admin\",\n        \"operations\"\n    ],\n    view_analytics: [\n        \"super_admin\",\n        \"sales\",\n        \"operations\"\n    ]\n};\n/**\n * 检查用户是否具有指定权限\n * @param permission 权限名称\n * @param userRole 用户角色\n * @returns 是否具有权限\n */ function hasPermission(permission, userRole) {\n    if (!userRole) return false;\n    const allowedRoles = PERMISSIONS[permission];\n    return allowedRoles.includes(userRole);\n}\n/**\n * 获取用户的所有权限\n * @param userRole 用户角色\n * @returns 权限列表\n */ function getUserPermissions(userRole) {\n    return Object.keys(PERMISSIONS).filter((permission)=>hasPermission(permission, userRole));\n}\n/**\n * 检查用户是否具有任一权限\n * @param permissions 权限列表\n * @param userRole 用户角色\n * @returns 是否具有任一权限\n */ function hasAnyPermission(permissions, userRole) {\n    return permissions.some((permission)=>hasPermission(permission, userRole));\n}\n/**\n * 检查用户是否具有所有权限\n * @param permissions 权限列表\n * @param userRole 用户角色\n * @returns 是否具有所有权限\n */ function hasAllPermissions(permissions, userRole) {\n    return permissions.every((permission)=>hasPermission(permission, userRole));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-themes/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/next-themes/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ J),\n/* harmony export */   useTheme: () => (/* binding */ z)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nvar M = (e, i, s, u, m, a, l, h)=>{\n    let d = document.documentElement, w = [\n        \"light\",\n        \"dark\"\n    ];\n    function p(n) {\n        (Array.isArray(e) ? e : [\n            e\n        ]).forEach((y)=>{\n            let k = y === \"class\", S = k && a ? m.map((f)=>a[f] || f) : m;\n            k ? (d.classList.remove(...S), d.classList.add(a && a[n] ? a[n] : n)) : d.setAttribute(y, n);\n        }), R(n);\n    }\n    function R(n) {\n        h && w.includes(n) && (d.style.colorScheme = n);\n    }\n    function c() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    if (u) p(u);\n    else try {\n        let n = localStorage.getItem(i) || s, y = l && n === \"system\" ? c() : n;\n        p(y);\n    } catch (n) {}\n};\n_c = M;\nvar b = [\n    \"light\",\n    \"dark\"\n], I = \"(prefers-color-scheme: dark)\", O = \"object\" == \"undefined\", x = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0), U = {\n    setTheme: (e)=>{},\n    themes: []\n}, z = ()=>{\n    _s();\n    var e;\n    return (e = react__WEBPACK_IMPORTED_MODULE_0__.useContext(x)) != null ? e : U;\n}, J = (e)=>{\n    _s1();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useContext(x) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, e.children) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(V, {\n        ...e\n    });\n}, N = [\n    \"light\",\n    \"dark\"\n], V = (param)=>{\n    let { forcedTheme: e, disableTransitionOnChange: i = !1, enableSystem: s = !0, enableColorScheme: u = !0, storageKey: m = \"theme\", themes: a = N, defaultTheme: l = s ? \"system\" : \"light\", attribute: h = \"data-theme\", value: d, children: w, nonce: p, scriptProps: R } = param;\n    _s2();\n    let [c, n] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"V.useState\": ()=>H(m, l)\n    }[\"V.useState\"]), [T, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"V.useState\": ()=>c === \"system\" ? E() : c\n    }[\"V.useState\"]), k = d ? Object.values(d) : a, S = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[S]\": (o)=>{\n            let r = o;\n            if (!r) return;\n            o === \"system\" && s && (r = E());\n            let v = d ? d[r] : r, C = i ? W(p) : null, P = document.documentElement, L = {\n                \"V.useCallback[S].L\": (g)=>{\n                    g === \"class\" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith(\"data-\") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));\n                }\n            }[\"V.useCallback[S].L\"];\n            if (Array.isArray(h) ? h.forEach(L) : L(h), u) {\n                let g = b.includes(l) ? l : null, D = b.includes(r) ? r : g;\n                P.style.colorScheme = D;\n            }\n            C == null || C();\n        }\n    }[\"V.useCallback[S]\"], [\n        p\n    ]), f = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[f]\": (o)=>{\n            let r = typeof o == \"function\" ? o(c) : o;\n            n(r);\n            try {\n                localStorage.setItem(m, r);\n            } catch (v) {}\n        }\n    }[\"V.useCallback[f]\"], [\n        c\n    ]), A = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[A]\": (o)=>{\n            let r = E(o);\n            y(r), c === \"system\" && s && !e && S(\"system\");\n        }\n    }[\"V.useCallback[A]\"], [\n        c,\n        e\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            let o = window.matchMedia(I);\n            return o.addListener(A), A(o), ({\n                \"V.useEffect\": ()=>o.removeListener(A)\n            })[\"V.useEffect\"];\n        }\n    }[\"V.useEffect\"], [\n        A\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            let o = {\n                \"V.useEffect.o\": (r)=>{\n                    r.key === m && (r.newValue ? n(r.newValue) : f(l));\n                }\n            }[\"V.useEffect.o\"];\n            return window.addEventListener(\"storage\", o), ({\n                \"V.useEffect\": ()=>window.removeEventListener(\"storage\", o)\n            })[\"V.useEffect\"];\n        }\n    }[\"V.useEffect\"], [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            S(e != null ? e : c);\n        }\n    }[\"V.useEffect\"], [\n        e,\n        c\n    ]);\n    let Q = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"V.useMemo[Q]\": ()=>({\n                theme: c,\n                setTheme: f,\n                forcedTheme: e,\n                resolvedTheme: c === \"system\" ? T : c,\n                themes: s ? [\n                    ...a,\n                    \"system\"\n                ] : a,\n                systemTheme: s ? T : void 0\n            })\n    }[\"V.useMemo[Q]\"], [\n        c,\n        f,\n        e,\n        T,\n        s,\n        a\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(x.Provider, {\n        value: Q\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_, {\n        forcedTheme: e,\n        storageKey: m,\n        attribute: h,\n        enableSystem: s,\n        enableColorScheme: u,\n        defaultTheme: l,\n        value: d,\n        themes: a,\n        nonce: p,\n        scriptProps: R\n    }), w);\n}, _ = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo((param)=>{\n    let { forcedTheme: e, storageKey: i, attribute: s, enableSystem: u, enableColorScheme: m, defaultTheme: a, value: l, themes: h, nonce: d, scriptProps: w } = param;\n    let p = JSON.stringify([\n        s,\n        i,\n        a,\n        e,\n        h,\n        l,\n        u,\n        m\n    ]).slice(1, -1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"script\", {\n        ...w,\n        suppressHydrationWarning: !0,\n        nonce:  false ? 0 : \"\",\n        dangerouslySetInnerHTML: {\n            __html: \"(\".concat(M.toString(), \")(\").concat(p, \")\")\n        }\n    });\n}), H = (e, i)=>{\n    if (O) return;\n    let s;\n    try {\n        s = localStorage.getItem(e) || void 0;\n    } catch (u) {}\n    return s || i;\n}, W = (e)=>{\n    let i = document.createElement(\"style\");\n    return e && i.setAttribute(\"nonce\", e), i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")), document.head.appendChild(i), ()=>{\n        window.getComputedStyle(document.body), setTimeout(()=>{\n            document.head.removeChild(i);\n        }, 1);\n    };\n}, E = (e)=>(e || (e = window.matchMedia(I)), e.matches ? \"dark\" : \"light\");\n_s(z, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s1(J, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s2(V, \"UCkmxL+2pKwquH5a3QithkhUKcE=\");\n\nvar _c;\n$RefreshReg$(_c, \"M\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(app-pages-browser)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/auth-context.tsx */ \"(app-pages-browser)/./contexts/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLCtDQUErQyw2QkFBNkI7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxnQkFBZ0IsZ0RBQWdEO0FBQ2hFLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQSxnQ0FBZ0Msa0NBQWtDLE9BQU87QUFDekU7QUFDQSxnR0FBZ0csU0FBUyxVQUFVLHNGQUFzRixhQUFhLFVBQVUsVUFBVTtBQUMxTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixtQkFBTyxDQUFDLHNHQUEwQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjY2FpXFxEZXNrdG9wXFxEZXZcXENlbGxGb3JnZSBBSVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxjanNcXHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiByZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cblwidXNlIHN0cmljdFwiO1xuXCJwcm9kdWN0aW9uXCIgIT09IHByb2Nlc3MuZW52Lk5PREVfRU5WICYmXG4gIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgICAgIGlmIChudWxsID09IHR5cGUpIHJldHVybiBudWxsO1xuICAgICAgaWYgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHJldHVybiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFXG4gICAgICAgICAgPyBudWxsXG4gICAgICAgICAgOiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICAgICAgaWYgKFwic3RyaW5nXCIgPT09IHR5cGVvZiB0eXBlKSByZXR1cm4gdHlwZTtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFJFQUNUX0ZSQUdNRU5UX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiRnJhZ21lbnRcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9QT1JUQUxfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJQb3J0YWxcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlByb2ZpbGVyXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1RSSUNUX01PREVfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdHJpY3RNb2RlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZUxpc3RcIjtcbiAgICAgIH1cbiAgICAgIGlmIChcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgc3dpdGNoIChcbiAgICAgICAgICAoXCJudW1iZXJcIiA9PT0gdHlwZW9mIHR5cGUudGFnICYmXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlY2VpdmVkIGFuIHVuZXhwZWN0ZWQgb2JqZWN0IGluIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSgpLiBUaGlzIGlzIGxpa2VseSBhIGJ1ZyBpbiBSZWFjdC4gUGxlYXNlIGZpbGUgYW4gaXNzdWUuXCJcbiAgICAgICAgICAgICksXG4gICAgICAgICAgdHlwZS4kJHR5cGVvZilcbiAgICAgICAgKSB7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05URVhUX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKHR5cGUuZGlzcGxheU5hbWUgfHwgXCJDb250ZXh0XCIpICsgXCIuUHJvdmlkZXJcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlNVTUVSX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKHR5cGUuX2NvbnRleHQuZGlzcGxheU5hbWUgfHwgXCJDb250ZXh0XCIpICsgXCIuQ29uc3VtZXJcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEU6XG4gICAgICAgICAgICB2YXIgaW5uZXJUeXBlID0gdHlwZS5yZW5kZXI7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5kaXNwbGF5TmFtZTtcbiAgICAgICAgICAgIHR5cGUgfHxcbiAgICAgICAgICAgICAgKCh0eXBlID0gaW5uZXJUeXBlLmRpc3BsYXlOYW1lIHx8IGlubmVyVHlwZS5uYW1lIHx8IFwiXCIpLFxuICAgICAgICAgICAgICAodHlwZSA9IFwiXCIgIT09IHR5cGUgPyBcIkZvcndhcmRSZWYoXCIgKyB0eXBlICsgXCIpXCIgOiBcIkZvcndhcmRSZWZcIikpO1xuICAgICAgICAgICAgcmV0dXJuIHR5cGU7XG4gICAgICAgICAgY2FzZSBSRUFDVF9NRU1PX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAoaW5uZXJUeXBlID0gdHlwZS5kaXNwbGF5TmFtZSB8fCBudWxsKSxcbiAgICAgICAgICAgICAgbnVsbCAhPT0gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgPyBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA6IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8IFwiTWVtb1wiXG4gICAgICAgICAgICApO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTEFaWV9UWVBFOlxuICAgICAgICAgICAgaW5uZXJUeXBlID0gdHlwZS5fcGF5bG9hZDtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLl9pbml0O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgcmV0dXJuIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKGlubmVyVHlwZSkpO1xuICAgICAgICAgICAgfSBjYXRjaCAoeCkge31cbiAgICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIFwiXCIgKyB2YWx1ZTtcbiAgICB9XG4gICAgZnVuY3Rpb24gY2hlY2tLZXlTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICExO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMDtcbiAgICAgIH1cbiAgICAgIGlmIChKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gY29uc29sZTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfdGVtcF9jb25zdCA9IEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdC5lcnJvcjtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMCA9XG4gICAgICAgICAgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIFN5bWJvbCAmJlxuICAgICAgICAgICAgU3ltYm9sLnRvU3RyaW5nVGFnICYmXG4gICAgICAgICAgICB2YWx1ZVtTeW1ib2wudG9TdHJpbmdUYWddKSB8fFxuICAgICAgICAgIHZhbHVlLmNvbnN0cnVjdG9yLm5hbWUgfHxcbiAgICAgICAgICBcIk9iamVjdFwiO1xuICAgICAgICBKU0NvbXBpbGVyX3RlbXBfY29uc3QuY2FsbChcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQsXG4gICAgICAgICAgXCJUaGUgcHJvdmlkZWQga2V5IGlzIGFuIHVuc3VwcG9ydGVkIHR5cGUgJXMuIFRoaXMgdmFsdWUgbXVzdCBiZSBjb2VyY2VkIHRvIGEgc3RyaW5nIGJlZm9yZSB1c2luZyBpdCBoZXJlLlwiLFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMFxuICAgICAgICApO1xuICAgICAgICByZXR1cm4gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0VGFza05hbWUodHlwZSkge1xuICAgICAgaWYgKHR5cGUgPT09IFJFQUNUX0ZSQUdNRU5UX1RZUEUpIHJldHVybiBcIjw+XCI7XG4gICAgICBpZiAoXG4gICAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlICYmXG4gICAgICAgIG51bGwgIT09IHR5cGUgJiZcbiAgICAgICAgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfTEFaWV9UWVBFXG4gICAgICApXG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB0cnkge1xuICAgICAgICB2YXIgbmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgcmV0dXJuIG5hbWUgPyBcIjxcIiArIG5hbWUgKyBcIj5cIiA6IFwiPC4uLj5cIjtcbiAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0T3duZXIoKSB7XG4gICAgICB2YXIgZGlzcGF0Y2hlciA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLkE7XG4gICAgICByZXR1cm4gbnVsbCA9PT0gZGlzcGF0Y2hlciA/IG51bGwgOiBkaXNwYXRjaGVyLmdldE93bmVyKCk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGhhc1ZhbGlkS2V5KGNvbmZpZykge1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICB2YXIgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihjb25maWcsIFwia2V5XCIpLmdldDtcbiAgICAgICAgaWYgKGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmcpIHJldHVybiAhMTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbmZpZy5rZXk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSkge1xuICAgICAgZnVuY3Rpb24gd2FybkFib3V0QWNjZXNzaW5nS2V5KCkge1xuICAgICAgICBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biB8fFxuICAgICAgICAgICgoc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gPSAhMCksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIFwiJXM6IGBrZXlgIGlzIG5vdCBhIHByb3AuIFRyeWluZyB0byBhY2Nlc3MgaXQgd2lsbCByZXN1bHQgaW4gYHVuZGVmaW5lZGAgYmVpbmcgcmV0dXJuZWQuIElmIHlvdSBuZWVkIHRvIGFjY2VzcyB0aGUgc2FtZSB2YWx1ZSB3aXRoaW4gdGhlIGNoaWxkIGNvbXBvbmVudCwgeW91IHNob3VsZCBwYXNzIGl0IGFzIGEgZGlmZmVyZW50IHByb3AuIChodHRwczovL3JlYWN0LmRldi9saW5rL3NwZWNpYWwtcHJvcHMpXCIsXG4gICAgICAgICAgICBkaXNwbGF5TmFtZVxuICAgICAgICAgICkpO1xuICAgICAgfVxuICAgICAgd2FybkFib3V0QWNjZXNzaW5nS2V5LmlzUmVhY3RXYXJuaW5nID0gITA7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvcHMsIFwia2V5XCIsIHtcbiAgICAgICAgZ2V0OiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXksXG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITBcbiAgICAgIH0pO1xuICAgIH1cbiAgICBmdW5jdGlvbiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZygpIHtcbiAgICAgIHZhciBjb21wb25lbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHRoaXMudHlwZSk7XG4gICAgICBkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdIHx8XG4gICAgICAgICgoZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSA9ICEwKSxcbiAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICBcIkFjY2Vzc2luZyBlbGVtZW50LnJlZiB3YXMgcmVtb3ZlZCBpbiBSZWFjdCAxOS4gcmVmIGlzIG5vdyBhIHJlZ3VsYXIgcHJvcC4gSXQgd2lsbCBiZSByZW1vdmVkIGZyb20gdGhlIEpTWCBFbGVtZW50IHR5cGUgaW4gYSBmdXR1cmUgcmVsZWFzZS5cIlxuICAgICAgICApKTtcbiAgICAgIGNvbXBvbmVudE5hbWUgPSB0aGlzLnByb3BzLnJlZjtcbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbXBvbmVudE5hbWUgPyBjb21wb25lbnROYW1lIDogbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gUmVhY3RFbGVtZW50KFxuICAgICAgdHlwZSxcbiAgICAgIGtleSxcbiAgICAgIHNlbGYsXG4gICAgICBzb3VyY2UsXG4gICAgICBvd25lcixcbiAgICAgIHByb3BzLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgc2VsZiA9IHByb3BzLnJlZjtcbiAgICAgIHR5cGUgPSB7XG4gICAgICAgICQkdHlwZW9mOiBSRUFDVF9FTEVNRU5UX1RZUEUsXG4gICAgICAgIHR5cGU6IHR5cGUsXG4gICAgICAgIGtleToga2V5LFxuICAgICAgICBwcm9wczogcHJvcHMsXG4gICAgICAgIF9vd25lcjogb3duZXJcbiAgICAgIH07XG4gICAgICBudWxsICE9PSAodm9pZCAwICE9PSBzZWxmID8gc2VsZiA6IG51bGwpXG4gICAgICAgID8gT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICAgICAgZ2V0OiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZ1xuICAgICAgICAgIH0pXG4gICAgICAgIDogT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHsgZW51bWVyYWJsZTogITEsIHZhbHVlOiBudWxsIH0pO1xuICAgICAgdHlwZS5fc3RvcmUgPSB7fTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLl9zdG9yZSwgXCJ2YWxpZGF0ZWRcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogMFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdJbmZvXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnU3RhY2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdTdGFja1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdUYXNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnVGFza1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZnJlZXplICYmIChPYmplY3QuZnJlZXplKHR5cGUucHJvcHMpLCBPYmplY3QuZnJlZXplKHR5cGUpKTtcbiAgICAgIHJldHVybiB0eXBlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBqc3hERVZJbXBsKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGYsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICB2YXIgY2hpbGRyZW4gPSBjb25maWcuY2hpbGRyZW47XG4gICAgICBpZiAodm9pZCAwICE9PSBjaGlsZHJlbilcbiAgICAgICAgaWYgKGlzU3RhdGljQ2hpbGRyZW4pXG4gICAgICAgICAgaWYgKGlzQXJyYXlJbXBsKGNoaWxkcmVuKSkge1xuICAgICAgICAgICAgZm9yIChcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9IDA7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPCBjaGlsZHJlbi5sZW5ndGg7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4rK1xuICAgICAgICAgICAgKVxuICAgICAgICAgICAgICB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbltpc1N0YXRpY0NoaWxkcmVuXSk7XG4gICAgICAgICAgICBPYmplY3QuZnJlZXplICYmIE9iamVjdC5mcmVlemUoY2hpbGRyZW4pO1xuICAgICAgICAgIH0gZWxzZVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWFjdC5qc3g6IFN0YXRpYyBjaGlsZHJlbiBzaG91bGQgYWx3YXlzIGJlIGFuIGFycmF5LiBZb3UgYXJlIGxpa2VseSBleHBsaWNpdGx5IGNhbGxpbmcgUmVhY3QuanN4cyBvciBSZWFjdC5qc3hERVYuIFVzZSB0aGUgQmFiZWwgdHJhbnNmb3JtIGluc3RlYWQuXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgIGVsc2UgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW4pO1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICBjaGlsZHJlbiA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhjb25maWcpLmZpbHRlcihmdW5jdGlvbiAoaykge1xuICAgICAgICAgIHJldHVybiBcImtleVwiICE9PSBrO1xuICAgICAgICB9KTtcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9XG4gICAgICAgICAgMCA8IGtleXMubGVuZ3RoXG4gICAgICAgICAgICA/IFwie2tleTogc29tZUtleSwgXCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIlxuICAgICAgICAgICAgOiBcIntrZXk6IHNvbWVLZXl9XCI7XG4gICAgICAgIGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dIHx8XG4gICAgICAgICAgKChrZXlzID1cbiAgICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aCA/IFwie1wiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCIgOiBcInt9XCIpLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAnQSBwcm9wcyBvYmplY3QgY29udGFpbmluZyBhIFwia2V5XCIgcHJvcCBpcyBiZWluZyBzcHJlYWQgaW50byBKU1g6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyB7Li4ucHJvcHN9IC8+XFxuUmVhY3Qga2V5cyBtdXN0IGJlIHBhc3NlZCBkaXJlY3RseSB0byBKU1ggd2l0aG91dCB1c2luZyBzcHJlYWQ6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyBrZXk9e3NvbWVLZXl9IHsuLi5wcm9wc30gLz4nLFxuICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgICAgIGNoaWxkcmVuLFxuICAgICAgICAgICAga2V5cyxcbiAgICAgICAgICAgIGNoaWxkcmVuXG4gICAgICAgICAgKSxcbiAgICAgICAgICAoZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gPSAhMCkpO1xuICAgICAgfVxuICAgICAgY2hpbGRyZW4gPSBudWxsO1xuICAgICAgdm9pZCAwICE9PSBtYXliZUtleSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihtYXliZUtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBtYXliZUtleSkpO1xuICAgICAgaGFzVmFsaWRLZXkoY29uZmlnKSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihjb25maWcua2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIGNvbmZpZy5rZXkpKTtcbiAgICAgIGlmIChcImtleVwiIGluIGNvbmZpZykge1xuICAgICAgICBtYXliZUtleSA9IHt9O1xuICAgICAgICBmb3IgKHZhciBwcm9wTmFtZSBpbiBjb25maWcpXG4gICAgICAgICAgXCJrZXlcIiAhPT0gcHJvcE5hbWUgJiYgKG1heWJlS2V5W3Byb3BOYW1lXSA9IGNvbmZpZ1twcm9wTmFtZV0pO1xuICAgICAgfSBlbHNlIG1heWJlS2V5ID0gY29uZmlnO1xuICAgICAgY2hpbGRyZW4gJiZcbiAgICAgICAgZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIoXG4gICAgICAgICAgbWF5YmVLZXksXG4gICAgICAgICAgXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZVxuICAgICAgICAgICAgPyB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBcIlVua25vd25cIlxuICAgICAgICAgICAgOiB0eXBlXG4gICAgICAgICk7XG4gICAgICByZXR1cm4gUmVhY3RFbGVtZW50KFxuICAgICAgICB0eXBlLFxuICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgc2VsZixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBnZXRPd25lcigpLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgZGVidWdTdGFjayxcbiAgICAgICAgZGVidWdUYXNrXG4gICAgICApO1xuICAgIH1cbiAgICBmdW5jdGlvbiB2YWxpZGF0ZUNoaWxkS2V5cyhub2RlKSB7XG4gICAgICBcIm9iamVjdFwiID09PSB0eXBlb2Ygbm9kZSAmJlxuICAgICAgICBudWxsICE9PSBub2RlICYmXG4gICAgICAgIG5vZGUuJCR0eXBlb2YgPT09IFJFQUNUX0VMRU1FTlRfVFlQRSAmJlxuICAgICAgICBub2RlLl9zdG9yZSAmJlxuICAgICAgICAobm9kZS5fc3RvcmUudmFsaWRhdGVkID0gMSk7XG4gICAgfVxuICAgIHZhciBSZWFjdCA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3RcIiksXG4gICAgICBSRUFDVF9FTEVNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QudHJhbnNpdGlvbmFsLmVsZW1lbnRcIiksXG4gICAgICBSRUFDVF9QT1JUQUxfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wb3J0YWxcIiksXG4gICAgICBSRUFDVF9GUkFHTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZyYWdtZW50XCIpLFxuICAgICAgUkVBQ1RfU1RSSUNUX01PREVfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdHJpY3RfbW9kZVwiKSxcbiAgICAgIFJFQUNUX1BST0ZJTEVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucHJvZmlsZXJcIik7XG4gICAgU3ltYm9sLmZvcihcInJlYWN0LnByb3ZpZGVyXCIpO1xuICAgIHZhciBSRUFDVF9DT05TVU1FUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnN1bWVyXCIpLFxuICAgICAgUkVBQ1RfQ09OVEVYVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnRleHRcIiksXG4gICAgICBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZvcndhcmRfcmVmXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZVwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZV9saXN0XCIpLFxuICAgICAgUkVBQ1RfTUVNT19UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0Lm1lbW9cIiksXG4gICAgICBSRUFDVF9MQVpZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubGF6eVwiKSxcbiAgICAgIFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UgPSBTeW1ib2wuZm9yKFwicmVhY3QuY2xpZW50LnJlZmVyZW5jZVwiKSxcbiAgICAgIFJlYWN0U2hhcmVkSW50ZXJuYWxzID1cbiAgICAgICAgUmVhY3QuX19DTElFTlRfSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFLFxuICAgICAgaGFzT3duUHJvcGVydHkgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LFxuICAgICAgaXNBcnJheUltcGwgPSBBcnJheS5pc0FycmF5LFxuICAgICAgY3JlYXRlVGFzayA9IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA/IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH0sXG4gICAgICBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bjtcbiAgICB2YXIgZGlkV2FybkFib3V0RWxlbWVudFJlZiA9IHt9O1xuICAgIHZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbiAgICBleHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbiAgICBleHBvcnRzLmpzeERFViA9IGZ1bmN0aW9uIChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmXG4gICAgKSB7XG4gICAgICByZXR1cm4ganN4REVWSW1wbChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBzZWxmLFxuICAgICAgICBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKSxcbiAgICAgICAgY3JlYXRlVGFzayhnZXRUYXNrTmFtZSh0eXBlKSlcbiAgICAgICk7XG4gICAgfTtcbiAgfSkoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Inter', 'Inter Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1753333396123\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyw4REFBOEQ7QUFDekYsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQXFJLGNBQWMsc0RBQXNEO0FBQ3ZPLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2NhaVxcRGVza3RvcFxcRGV2XFxDZWxsRm9yZ2UgQUlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXG5leHRcXGZvbnRcXGdvb2dsZVxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJhcHBcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn18YXBwLXBhZ2VzLWJyb3dzZXIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ0ludGVyJywgJ0ludGVyIEZhbGxiYWNrJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lX2U4Y2UwY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUzMzMzMzk2MTIzXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL2NjYWkvRGVza3RvcC9EZXYvQ2VsbEZvcmdlIEFJL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ $e),\n/* harmony export */   toast: () => (/* binding */ ue),\n/* harmony export */   useSonner: () => (/* binding */ Oe)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nvar jt = (n)=>{\n    switch(n){\n        case \"success\":\n            return ee;\n        case \"info\":\n            return ae;\n        case \"warning\":\n            return oe;\n        case \"error\":\n            return se;\n        default:\n            return null;\n    }\n}, te = Array(12).fill(0), Yt = (param)=>{\n    let { visible: n, className: e } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            \"sonner-loading-wrapper\",\n            e\n        ].filter(Boolean).join(\" \"),\n        \"data-visible\": n\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, te.map((t, a)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: \"spinner-bar-\".concat(a)\n        }))));\n}, ee = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), oe = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), ae = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), se = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n})), Ot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nvar Ft = ()=>{\n    _s();\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Ft.useEffect\": ()=>{\n            let t = {\n                \"Ft.useEffect.t\": ()=>{\n                    e(document.hidden);\n                }\n            }[\"Ft.useEffect.t\"];\n            return document.addEventListener(\"visibilitychange\", t), ({\n                \"Ft.useEffect\": ()=>window.removeEventListener(\"visibilitychange\", t)\n            })[\"Ft.useEffect\"];\n        }\n    }[\"Ft.useEffect\"], []), n;\n};\n_s(Ft, \"C3CD/+xToPnCp6mg1TPF29IUkt8=\");\n_c = Ft;\n\nvar bt = 1, yt = class {\n    constructor(){\n        this.subscribe = (e)=>(this.subscribers.push(e), ()=>{\n                let t = this.subscribers.indexOf(e);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (e)=>{\n            this.subscribers.forEach((t)=>t(e));\n        };\n        this.addToast = (e)=>{\n            this.publish(e), this.toasts = [\n                ...this.toasts,\n                e\n            ];\n        };\n        this.create = (e)=>{\n            var S;\n            let { message: t, ...a } = e, u = typeof (e == null ? void 0 : e.id) == \"number\" || ((S = e.id) == null ? void 0 : S.length) > 0 ? e.id : bt++, f = this.toasts.find((g)=>g.id === u), w = e.dismissible === void 0 ? !0 : e.dismissible;\n            return this.dismissedToasts.has(u) && this.dismissedToasts.delete(u), f ? this.toasts = this.toasts.map((g)=>g.id === u ? (this.publish({\n                    ...g,\n                    ...e,\n                    id: u,\n                    title: t\n                }), {\n                    ...g,\n                    ...e,\n                    id: u,\n                    dismissible: w,\n                    title: t\n                }) : g) : this.addToast({\n                title: t,\n                ...a,\n                dismissible: w,\n                id: u\n            }), u;\n        };\n        this.dismiss = (e)=>(this.dismissedToasts.add(e), e || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((a)=>a({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: e,\n                    dismiss: !0\n                })), e);\n        this.message = (e, t)=>this.create({\n                ...t,\n                message: e\n            });\n        this.error = (e, t)=>this.create({\n                ...t,\n                message: e,\n                type: \"error\"\n            });\n        this.success = (e, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: e\n            });\n        this.info = (e, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: e\n            });\n        this.warning = (e, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: e\n            });\n        this.loading = (e, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: e\n            });\n        this.promise = (e, t)=>{\n            if (!t) return;\n            let a;\n            t.loading !== void 0 && (a = this.create({\n                ...t,\n                promise: e,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let u = e instanceof Promise ? e : e(), f = a !== void 0, w, S = u.then(async (i)=>{\n                if (w = [\n                    \"resolve\",\n                    i\n                ], /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(i)) f = !1, this.create({\n                    id: a,\n                    type: \"default\",\n                    message: i\n                });\n                else if (ie(i) && !i.ok) {\n                    f = !1;\n                    let T = typeof t.error == \"function\" ? await t.error(\"HTTP error! status: \".concat(i.status)) : t.error, F = typeof t.description == \"function\" ? await t.description(\"HTTP error! status: \".concat(i.status)) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: T,\n                        description: F\n                    });\n                } else if (t.success !== void 0) {\n                    f = !1;\n                    let T = typeof t.success == \"function\" ? await t.success(i) : t.success, F = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"success\",\n                        message: T,\n                        description: F\n                    });\n                }\n            }).catch(async (i)=>{\n                if (w = [\n                    \"reject\",\n                    i\n                ], t.error !== void 0) {\n                    f = !1;\n                    let D = typeof t.error == \"function\" ? await t.error(i) : t.error, T = typeof t.description == \"function\" ? await t.description(i) : t.description;\n                    this.create({\n                        id: a,\n                        type: \"error\",\n                        message: D,\n                        description: T\n                    });\n                }\n            }).finally(()=>{\n                var i;\n                f && (this.dismiss(a), a = void 0), (i = t.finally) == null || i.call(t);\n            }), g = ()=>new Promise((i, D)=>S.then(()=>w[0] === \"reject\" ? D(w[1]) : i(w[1])).catch(D));\n            return typeof a != \"string\" && typeof a != \"number\" ? {\n                unwrap: g\n            } : Object.assign(a, {\n                unwrap: g\n            });\n        };\n        this.custom = (e, t)=>{\n            let a = (t == null ? void 0 : t.id) || bt++;\n            return this.create({\n                jsx: e(a),\n                id: a,\n                ...t\n            }), a;\n        };\n        this.getActiveToasts = ()=>this.toasts.filter((e)=>!this.dismissedToasts.has(e.id));\n        this.subscribers = [], this.toasts = [], this.dismissedToasts = new Set;\n    }\n}, v = new yt, ne = (n, e)=>{\n    let t = (e == null ? void 0 : e.id) || bt++;\n    return v.addToast({\n        title: n,\n        ...e,\n        id: t\n    }), t;\n}, ie = (n)=>n && typeof n == \"object\" && \"ok\" in n && typeof n.ok == \"boolean\" && \"status\" in n && typeof n.status == \"number\", le = ne, ce = ()=>v.toasts, de = ()=>v.getActiveToasts(), ue = Object.assign(le, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: ce,\n    getToasts: de\n});\nfunction wt(n) {\n    let { insertAt: e } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    if (!n || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], a = document.createElement(\"style\");\n    a.type = \"text/css\", e === \"top\" && t.firstChild ? t.insertBefore(a, t.firstChild) : t.appendChild(a), a.styleSheet ? a.styleSheet.cssText = n : a.appendChild(document.createTextNode(n));\n}\nwt(':where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\"true\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\"true\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\"right\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\"left\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\\n');\nfunction tt(n) {\n    return n.label !== void 0;\n}\nvar pe = 3, me = \"32px\", ge = \"16px\", Wt = 4e3, he = 356, be = 14, ye = 20, we = 200;\nfunction M() {\n    for(var _len = arguments.length, n = new Array(_len), _key = 0; _key < _len; _key++){\n        n[_key] = arguments[_key];\n    }\n    return n.filter(Boolean).join(\" \");\n}\n_c1 = M;\nfunction xe(n) {\n    let [e, t] = n.split(\"-\"), a = [];\n    return e && a.push(e), t && a.push(t), a;\n}\nvar ve = (n)=>{\n    _s1();\n    var Dt, Pt, Nt, Bt, Ct, kt, It, Mt, Ht, At, Lt;\n    let { invert: e, toast: t, unstyled: a, interacting: u, setHeights: f, visibleToasts: w, heights: S, index: g, toasts: i, expanded: D, removeToast: T, defaultRichColors: F, closeButton: et, style: ut, cancelButtonStyle: ft, actionButtonStyle: l, className: ot = \"\", descriptionClassName: at = \"\", duration: X, position: st, gap: pt, loadingIcon: rt, expandByDefault: B, classNames: s, icons: P, closeButtonAriaLabel: nt = \"Close toast\", pauseWhenPageIsHidden: it } = n, [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [A, mt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [L, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [ct, d] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [h, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [R, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [p, _] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), O = react__WEBPACK_IMPORTED_MODULE_0__.useRef(t.duration || X || Wt), G = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), k = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), Vt = g === 0, Ut = g + 1 <= w, N = t.type, V = t.dismissible !== !1, Kt = t.className || \"\", Xt = t.descriptionClassName || \"\", dt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[dt]\": ()=>S.findIndex({\n                \"ve.useMemo[dt]\": (r)=>r.toastId === t.id\n            }[\"ve.useMemo[dt]\"]) || 0\n    }[\"ve.useMemo[dt]\"], [\n        S,\n        t.id\n    ]), Jt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[Jt]\": ()=>{\n            var r;\n            return (r = t.closeButton) != null ? r : et;\n        }\n    }[\"ve.useMemo[Jt]\"], [\n        t.closeButton,\n        et\n    ]), Tt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[Tt]\": ()=>t.duration || X || Wt\n    }[\"ve.useMemo[Tt]\"], [\n        t.duration,\n        X\n    ]), gt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), U = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), St = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), K = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [Gt, Qt] = st.split(\"-\"), Rt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo[Rt]\": ()=>S.reduce({\n                \"ve.useMemo[Rt]\": (r, m, c)=>c >= dt ? r : r + m.height\n            }[\"ve.useMemo[Rt]\"], 0)\n    }[\"ve.useMemo[Rt]\"], [\n        S,\n        dt\n    ]), Et = Ft(), qt = t.invert || e, ht = N === \"loading\";\n    U.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"ve.useMemo\": ()=>dt * pt + Rt\n    }[\"ve.useMemo\"], [\n        dt,\n        Rt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            O.current = Tt;\n        }\n    }[\"ve.useEffect\"], [\n        Tt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            H(!0);\n        }\n    }[\"ve.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            let r = k.current;\n            if (r) {\n                let m = r.getBoundingClientRect().height;\n                return _(m), f({\n                    \"ve.useEffect\": (c)=>[\n                            {\n                                toastId: t.id,\n                                height: m,\n                                position: t.position\n                            },\n                            ...c\n                        ]\n                }[\"ve.useEffect\"]), ({\n                    \"ve.useEffect\": ()=>f({\n                            \"ve.useEffect\": (c)=>c.filter({\n                                    \"ve.useEffect\": (b)=>b.toastId !== t.id\n                                }[\"ve.useEffect\"])\n                        }[\"ve.useEffect\"])\n                })[\"ve.useEffect\"];\n            }\n        }\n    }[\"ve.useEffect\"], [\n        f,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"ve.useLayoutEffect\": ()=>{\n            if (!W) return;\n            let r = k.current, m = r.style.height;\n            r.style.height = \"auto\";\n            let c = r.getBoundingClientRect().height;\n            r.style.height = m, _(c), f({\n                \"ve.useLayoutEffect\": (b)=>b.find({\n                        \"ve.useLayoutEffect\": (x)=>x.toastId === t.id\n                    }[\"ve.useLayoutEffect\"]) ? b.map({\n                        \"ve.useLayoutEffect\": (x)=>x.toastId === t.id ? {\n                                ...x,\n                                height: c\n                            } : x\n                    }[\"ve.useLayoutEffect\"]) : [\n                        {\n                            toastId: t.id,\n                            height: c,\n                            position: t.position\n                        },\n                        ...b\n                    ]\n            }[\"ve.useLayoutEffect\"]);\n        }\n    }[\"ve.useLayoutEffect\"], [\n        W,\n        t.title,\n        t.description,\n        f,\n        t.id\n    ]);\n    let $ = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"ve.useCallback[$]\": ()=>{\n            mt(!0), j(U.current), f({\n                \"ve.useCallback[$]\": (r)=>r.filter({\n                        \"ve.useCallback[$]\": (m)=>m.toastId !== t.id\n                    }[\"ve.useCallback[$]\"])\n            }[\"ve.useCallback[$]\"]), setTimeout({\n                \"ve.useCallback[$]\": ()=>{\n                    T(t);\n                }\n            }[\"ve.useCallback[$]\"], we);\n        }\n    }[\"ve.useCallback[$]\"], [\n        t,\n        T,\n        f,\n        U\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            if (t.promise && N === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n            let r;\n            return D || u || it && Et ? ({\n                \"ve.useEffect\": ()=>{\n                    if (St.current < gt.current) {\n                        let b = new Date().getTime() - gt.current;\n                        O.current = O.current - b;\n                    }\n                    St.current = new Date().getTime();\n                }\n            })[\"ve.useEffect\"]() : ({\n                \"ve.useEffect\": ()=>{\n                    O.current !== 1 / 0 && (gt.current = new Date().getTime(), r = setTimeout({\n                        \"ve.useEffect\": ()=>{\n                            var b;\n                            (b = t.onAutoClose) == null || b.call(t, t), $();\n                        }\n                    }[\"ve.useEffect\"], O.current));\n                }\n            })[\"ve.useEffect\"](), ({\n                \"ve.useEffect\": ()=>clearTimeout(r)\n            })[\"ve.useEffect\"];\n        }\n    }[\"ve.useEffect\"], [\n        D,\n        u,\n        t,\n        N,\n        it,\n        Et,\n        $\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ve.useEffect\": ()=>{\n            t.delete && $();\n        }\n    }[\"ve.useEffect\"], [\n        $,\n        t.delete\n    ]);\n    function Zt() {\n        var r, m, c;\n        return P != null && P.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (r = t == null ? void 0 : t.classNames) == null ? void 0 : r.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, P.loading) : rt ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: M(s == null ? void 0 : s.loader, (m = t == null ? void 0 : t.classNames) == null ? void 0 : m.loader, \"sonner-loader\"),\n            \"data-visible\": N === \"loading\"\n        }, rt) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Yt, {\n            className: M(s == null ? void 0 : s.loader, (c = t == null ? void 0 : t.classNames) == null ? void 0 : c.loader),\n            visible: N === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: k,\n        className: M(ot, Kt, s == null ? void 0 : s.toast, (Dt = t == null ? void 0 : t.classNames) == null ? void 0 : Dt.toast, s == null ? void 0 : s.default, s == null ? void 0 : s[N], (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt[N]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (Nt = t.richColors) != null ? Nt : F,\n        \"data-styled\": !(t.jsx || t.unstyled || a),\n        \"data-mounted\": W,\n        \"data-promise\": !!t.promise,\n        \"data-swiped\": h,\n        \"data-removed\": A,\n        \"data-visible\": Ut,\n        \"data-y-position\": Gt,\n        \"data-x-position\": Qt,\n        \"data-index\": g,\n        \"data-front\": Vt,\n        \"data-swiping\": L,\n        \"data-dismissible\": V,\n        \"data-type\": N,\n        \"data-invert\": qt,\n        \"data-swipe-out\": ct,\n        \"data-swipe-direction\": lt,\n        \"data-expanded\": !!(D || B && W),\n        style: {\n            \"--index\": g,\n            \"--toasts-before\": g,\n            \"--z-index\": i.length - g,\n            \"--offset\": \"\".concat(A ? R : U.current, \"px\"),\n            \"--initial-height\": B ? \"auto\" : \"\".concat(p, \"px\"),\n            ...ut,\n            ...t.style\n        },\n        onDragEnd: ()=>{\n            z(!1), C(null), K.current = null;\n        },\n        onPointerDown: (r)=>{\n            ht || !V || (G.current = new Date, j(U.current), r.target.setPointerCapture(r.pointerId), r.target.tagName !== \"BUTTON\" && (z(!0), K.current = {\n                x: r.clientX,\n                y: r.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var x, Q, q, Z;\n            if (ct || !V) return;\n            K.current = null;\n            let r = Number(((x = k.current) == null ? void 0 : x.style.getPropertyValue(\"--swipe-amount-x\").replace(\"px\", \"\")) || 0), m = Number(((Q = k.current) == null ? void 0 : Q.style.getPropertyValue(\"--swipe-amount-y\").replace(\"px\", \"\")) || 0), c = new Date().getTime() - ((q = G.current) == null ? void 0 : q.getTime()), b = Y === \"x\" ? r : m, I = Math.abs(b) / c;\n            if (Math.abs(b) >= ye || I > .11) {\n                j(U.current), (Z = t.onDismiss) == null || Z.call(t, t), J(Y === \"x\" ? r > 0 ? \"right\" : \"left\" : m > 0 ? \"down\" : \"up\"), $(), d(!0), y(!1);\n                return;\n            }\n            z(!1), C(null);\n        },\n        onPointerMove: (r)=>{\n            var Q, q, Z, zt;\n            if (!K.current || !V || ((Q = window.getSelection()) == null ? void 0 : Q.toString().length) > 0) return;\n            let c = r.clientY - K.current.y, b = r.clientX - K.current.x, I = (q = n.swipeDirections) != null ? q : xe(st);\n            !Y && (Math.abs(b) > 1 || Math.abs(c) > 1) && C(Math.abs(b) > Math.abs(c) ? \"x\" : \"y\");\n            let x = {\n                x: 0,\n                y: 0\n            };\n            Y === \"y\" ? (I.includes(\"top\") || I.includes(\"bottom\")) && (I.includes(\"top\") && c < 0 || I.includes(\"bottom\") && c > 0) && (x.y = c) : Y === \"x\" && (I.includes(\"left\") || I.includes(\"right\")) && (I.includes(\"left\") && b < 0 || I.includes(\"right\") && b > 0) && (x.x = b), (Math.abs(x.x) > 0 || Math.abs(x.y) > 0) && y(!0), (Z = k.current) == null || Z.style.setProperty(\"--swipe-amount-x\", \"\".concat(x.x, \"px\")), (zt = k.current) == null || zt.style.setProperty(\"--swipe-amount-y\", \"\".concat(x.y, \"px\"));\n        }\n    }, Jt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": nt,\n        \"data-disabled\": ht,\n        \"data-close-button\": !0,\n        onClick: ht || !V ? ()=>{} : ()=>{\n            var r;\n            $(), (r = t.onDismiss) == null || r.call(t, t);\n        },\n        className: M(s == null ? void 0 : s.closeButton, (Bt = t == null ? void 0 : t.classNames) == null ? void 0 : Bt.closeButton)\n    }, (Ct = P == null ? void 0 : P.close) != null ? Ct : Ot) : null, t.jsx || /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.title) ? t.jsx ? t.jsx : typeof t.title == \"function\" ? t.title() : t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, N || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: M(s == null ? void 0 : s.icon, (kt = t == null ? void 0 : t.classNames) == null ? void 0 : kt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Zt() : null, t.type !== \"loading\" ? t.icon || (P == null ? void 0 : P[N]) || jt(N) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: M(s == null ? void 0 : s.content, (It = t == null ? void 0 : t.classNames) == null ? void 0 : It.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: M(s == null ? void 0 : s.title, (Mt = t == null ? void 0 : t.classNames) == null ? void 0 : Mt.title)\n    }, typeof t.title == \"function\" ? t.title() : t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: M(at, Xt, s == null ? void 0 : s.description, (Ht = t == null ? void 0 : t.classNames) == null ? void 0 : Ht.description)\n    }, typeof t.description == \"function\" ? t.description() : t.description) : null), /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.cancel) ? t.cancel : t.cancel && tt(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || ft,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.cancel) && V && ((c = (m = t.cancel).onClick) == null || c.call(m, r), $());\n        },\n        className: M(s == null ? void 0 : s.cancelButton, (At = t == null ? void 0 : t.classNames) == null ? void 0 : At.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(t.action) ? t.action : t.action && tt(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || l,\n        onClick: (r)=>{\n            var m, c;\n            tt(t.action) && ((c = (m = t.action).onClick) == null || c.call(m, r), !r.defaultPrevented && $());\n        },\n        className: M(s == null ? void 0 : s.actionButton, (Lt = t == null ? void 0 : t.classNames) == null ? void 0 : Lt.actionButton)\n    }, t.action.label) : null));\n};\n_s1(ve, \"IK1IwnJnYM6KtZifqyNurGLRdJc=\");\n_c2 = ve;\nfunction _t() {\n    if (typeof window == \"undefined\" || typeof document == \"undefined\") return \"ltr\";\n    let n = document.documentElement.getAttribute(\"dir\");\n    return n === \"auto\" || !n ? window.getComputedStyle(document.documentElement).direction : n;\n}\nfunction Te(n, e) {\n    let t = {};\n    return [\n        n,\n        e\n    ].forEach((a, u)=>{\n        let f = u === 1, w = f ? \"--mobile-offset\" : \"--offset\", S = f ? ge : me;\n        function g(i) {\n            [\n                \"top\",\n                \"right\",\n                \"bottom\",\n                \"left\"\n            ].forEach((D)=>{\n                t[\"\".concat(w, \"-\").concat(D)] = typeof i == \"number\" ? \"\".concat(i, \"px\") : i;\n            });\n        }\n        typeof a == \"number\" || typeof a == \"string\" ? g(a) : typeof a == \"object\" ? [\n            \"top\",\n            \"right\",\n            \"bottom\",\n            \"left\"\n        ].forEach((i)=>{\n            a[i] === void 0 ? t[\"\".concat(w, \"-\").concat(i)] = S : t[\"\".concat(w, \"-\").concat(i)] = typeof a[i] == \"number\" ? \"\".concat(a[i], \"px\") : a[i];\n        }) : g(S);\n    }), t;\n}\n_c3 = Te;\nfunction Oe() {\n    _s2();\n    let [n, e] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Oe.useEffect\": ()=>v.subscribe({\n                \"Oe.useEffect\": (t)=>{\n                    if (t.dismiss) {\n                        setTimeout({\n                            \"Oe.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"Oe.useEffect\": ()=>{\n                                        e({\n                                            \"Oe.useEffect\": (a)=>a.filter({\n                                                    \"Oe.useEffect\": (u)=>u.id !== t.id\n                                                }[\"Oe.useEffect\"])\n                                        }[\"Oe.useEffect\"]);\n                                    }\n                                }[\"Oe.useEffect\"]);\n                            }\n                        }[\"Oe.useEffect\"]);\n                        return;\n                    }\n                    setTimeout({\n                        \"Oe.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Oe.useEffect\": ()=>{\n                                    e({\n                                        \"Oe.useEffect\": (a)=>{\n                                            let u = a.findIndex({\n                                                \"Oe.useEffect.u\": (f)=>f.id === t.id\n                                            }[\"Oe.useEffect.u\"]);\n                                            return u !== -1 ? [\n                                                ...a.slice(0, u),\n                                                {\n                                                    ...a[u],\n                                                    ...t\n                                                },\n                                                ...a.slice(u + 1)\n                                            ] : [\n                                                t,\n                                                ...a\n                                            ];\n                                        }\n                                    }[\"Oe.useEffect\"]);\n                                }\n                            }[\"Oe.useEffect\"]);\n                        }\n                    }[\"Oe.useEffect\"]);\n                }\n            }[\"Oe.useEffect\"])\n    }[\"Oe.useEffect\"], []), {\n        toasts: n\n    };\n}\n_s2(Oe, \"5C+wNK2Il/mi6buQK/AuxIesC3M=\");\n_c4 = Oe;\nvar $e = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_s3(function(e, t) {\n    _s3();\n    let { invert: a, position: u = \"bottom-right\", hotkey: f = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: w, closeButton: S, className: g, offset: i, mobileOffset: D, theme: T = \"light\", richColors: F, duration: et, style: ut, visibleToasts: ft = pe, toastOptions: l, dir: ot = _t(), gap: at = be, loadingIcon: X, icons: st, containerAriaLabel: pt = \"Notifications\", pauseWhenPageIsHidden: rt } = e, [B, s] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), P = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"$e.useMemo[P]\": ()=>Array.from(new Set([\n                u\n            ].concat(B.filter({\n                \"$e.useMemo[P]\": (d)=>d.position\n            }[\"$e.useMemo[P]\"]).map({\n                \"$e.useMemo[P]\": (d)=>d.position\n            }[\"$e.useMemo[P]\"]))))\n    }[\"$e.useMemo[P]\"], [\n        B,\n        u\n    ]), [nt, it] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [Y, C] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [lt, J] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [W, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(T !== \"system\" ? T : typeof window != \"undefined\" && window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\"), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), mt = f.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), L = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), z = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), ct = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"$e.useCallback[ct]\": (d)=>{\n            s({\n                \"$e.useCallback[ct]\": (h)=>{\n                    var y;\n                    return (y = h.find({\n                        \"$e.useCallback[ct]\": (R)=>R.id === d.id\n                    }[\"$e.useCallback[ct]\"])) != null && y.delete || v.dismiss(d.id), h.filter({\n                        \"$e.useCallback[ct]\": (param)=>{\n                            let { id: R } = param;\n                            return R !== d.id;\n                        }\n                    }[\"$e.useCallback[ct]\"]);\n                }\n            }[\"$e.useCallback[ct]\"]);\n        }\n    }[\"$e.useCallback[ct]\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>v.subscribe({\n                \"$e.useEffect\": (d)=>{\n                    if (d.dismiss) {\n                        s({\n                            \"$e.useEffect\": (h)=>h.map({\n                                    \"$e.useEffect\": (y)=>y.id === d.id ? {\n                                            ...y,\n                                            delete: !0\n                                        } : y\n                                }[\"$e.useEffect\"])\n                        }[\"$e.useEffect\"]);\n                        return;\n                    }\n                    setTimeout({\n                        \"$e.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"$e.useEffect\": ()=>{\n                                    s({\n                                        \"$e.useEffect\": (h)=>{\n                                            let y = h.findIndex({\n                                                \"$e.useEffect.y\": (R)=>R.id === d.id\n                                            }[\"$e.useEffect.y\"]);\n                                            return y !== -1 ? [\n                                                ...h.slice(0, y),\n                                                {\n                                                    ...h[y],\n                                                    ...d\n                                                },\n                                                ...h.slice(y + 1)\n                                            ] : [\n                                                d,\n                                                ...h\n                                            ];\n                                        }\n                                    }[\"$e.useEffect\"]);\n                                }\n                            }[\"$e.useEffect\"]);\n                        }\n                    }[\"$e.useEffect\"]);\n                }\n            }[\"$e.useEffect\"])\n    }[\"$e.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            if (T !== \"system\") {\n                H(T);\n                return;\n            }\n            if (T === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? H(\"dark\") : H(\"light\")), typeof window == \"undefined\") return;\n            let d = window.matchMedia(\"(prefers-color-scheme: dark)\");\n            try {\n                d.addEventListener(\"change\", {\n                    \"$e.useEffect\": (param)=>{\n                        let { matches: h } = param;\n                        H(h ? \"dark\" : \"light\");\n                    }\n                }[\"$e.useEffect\"]);\n            } catch (h) {\n                d.addListener({\n                    \"$e.useEffect\": (param)=>{\n                        let { matches: y } = param;\n                        try {\n                            H(y ? \"dark\" : \"light\");\n                        } catch (R) {\n                            console.error(R);\n                        }\n                    }\n                }[\"$e.useEffect\"]);\n            }\n        }\n    }[\"$e.useEffect\"], [\n        T\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            B.length <= 1 && C(!1);\n        }\n    }[\"$e.useEffect\"], [\n        B\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            let d = {\n                \"$e.useEffect.d\": (h)=>{\n                    var R, j;\n                    f.every({\n                        \"$e.useEffect.d\": (p)=>h[p] || h.code === p\n                    }[\"$e.useEffect.d\"]) && (C(!0), (R = A.current) == null || R.focus()), h.code === \"Escape\" && (document.activeElement === A.current || (j = A.current) != null && j.contains(document.activeElement)) && C(!1);\n                }\n            }[\"$e.useEffect.d\"];\n            return document.addEventListener(\"keydown\", d), ({\n                \"$e.useEffect\": ()=>document.removeEventListener(\"keydown\", d)\n            })[\"$e.useEffect\"];\n        }\n    }[\"$e.useEffect\"], [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"$e.useEffect\": ()=>{\n            if (A.current) return ({\n                \"$e.useEffect\": ()=>{\n                    L.current && (L.current.focus({\n                        preventScroll: !0\n                    }), L.current = null, z.current = !1);\n                }\n            })[\"$e.useEffect\"];\n        }\n    }[\"$e.useEffect\"], [\n        A.current\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: t,\n        \"aria-label\": \"\".concat(pt, \" \").concat(mt),\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: !0\n    }, P.map((d, h)=>{\n        var j;\n        let [y, R] = d.split(\"-\");\n        return B.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: d,\n            dir: ot === \"auto\" ? _t() : ot,\n            tabIndex: -1,\n            ref: A,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": W,\n            \"data-y-position\": y,\n            \"data-lifted\": Y && B.length > 1 && !w,\n            \"data-x-position\": R,\n            style: {\n                \"--front-toast-height\": \"\".concat(((j = nt[0]) == null ? void 0 : j.height) || 0, \"px\"),\n                \"--width\": \"\".concat(he, \"px\"),\n                \"--gap\": \"\".concat(at, \"px\"),\n                ...ut,\n                ...Te(i, D)\n            },\n            onBlur: (p)=>{\n                z.current && !p.currentTarget.contains(p.relatedTarget) && (z.current = !1, L.current && (L.current.focus({\n                    preventScroll: !0\n                }), L.current = null));\n            },\n            onFocus: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || z.current || (z.current = !0, L.current = p.relatedTarget);\n            },\n            onMouseEnter: ()=>C(!0),\n            onMouseMove: ()=>C(!0),\n            onMouseLeave: ()=>{\n                lt || C(!1);\n            },\n            onDragEnd: ()=>C(!1),\n            onPointerDown: (p)=>{\n                p.target instanceof HTMLElement && p.target.dataset.dismissible === \"false\" || J(!0);\n            },\n            onPointerUp: ()=>J(!1)\n        }, B.filter((p)=>!p.position && h === 0 || p.position === d).map((p, _)=>{\n            var O, G;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(ve, {\n                key: p.id,\n                icons: st,\n                index: _,\n                toast: p,\n                defaultRichColors: F,\n                duration: (O = l == null ? void 0 : l.duration) != null ? O : et,\n                className: l == null ? void 0 : l.className,\n                descriptionClassName: l == null ? void 0 : l.descriptionClassName,\n                invert: a,\n                visibleToasts: ft,\n                closeButton: (G = l == null ? void 0 : l.closeButton) != null ? G : S,\n                interacting: lt,\n                position: d,\n                style: l == null ? void 0 : l.style,\n                unstyled: l == null ? void 0 : l.unstyled,\n                classNames: l == null ? void 0 : l.classNames,\n                cancelButtonStyle: l == null ? void 0 : l.cancelButtonStyle,\n                actionButtonStyle: l == null ? void 0 : l.actionButtonStyle,\n                removeToast: ct,\n                toasts: B.filter((k)=>k.position == p.position),\n                heights: nt.filter((k)=>k.position == p.position),\n                setHeights: it,\n                expandByDefault: w,\n                gap: at,\n                loadingIcon: X,\n                expanded: Y,\n                pauseWhenPageIsHidden: rt,\n                swipeDirections: e.swipeDirections\n            });\n        })) : null;\n    }));\n}, \"YmYe6CxgXRDGml5ZcHYF4SdRNtM=\"));\n //# sourceMappingURL=index.mjs.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Ft\");\n$RefreshReg$(_c1, \"M\");\n$RefreshReg$(_c2, \"ve\");\n$RefreshReg$(_c3, \"Te\");\n$RefreshReg$(_c4, \"Oe\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/sonner/dist/index.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Ccontexts%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cccai%5C%5CDesktop%5C%5CDev%5C%5CCellForge%20AI%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);