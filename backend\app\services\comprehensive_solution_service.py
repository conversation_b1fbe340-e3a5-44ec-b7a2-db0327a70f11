"""
综合解决方案框架服务
基于COMPREHENSIVE_SOLUTION_FRAMEWORK_INTEGRATION.md实现
整合研究意图分析、精准搜索链接、方案展示等所有功能
"""
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from app.services.research_intent_service import get_research_intent_service
from app.services.enhanced_solution_service import get_enhanced_solution_service
from app.services.ai_service import AIService

logger = logging.getLogger(__name__)


class ComprehensiveSolutionService:
    """综合解决方案框架服务"""
    
    def __init__(self):
        self.research_intent_service = get_research_intent_service()
        self.enhanced_solution_service = get_enhanced_solution_service()
        self.ai_service = AIService()
        
        # 方案模板配置
        # 新的框架模板（按用户要求调整：方案概览、风险评估、研究建议、文献推荐）
        self.framework_templates = {
            "standard": {
                "name": "标准版方案",
                "components": ["solution_overview", "risk_assessment", "research_recommendations", "literature_recommendations"],
                "complexity": "medium",
                "target_users": "研究人员、实验室"
            },
            "detailed": {
                "name": "详细版方案",
                "components": ["solution_overview", "risk_assessment", "research_recommendations", "literature_recommendations"],
                "complexity": "high",
                "target_users": "项目负责人、技术专家"
            },
            "simplified": {
                "name": "简化版方案",
                "components": ["solution_overview", "risk_assessment", "literature_recommendations"],
                "complexity": "low",
                "target_users": "初学者、快速决策"
            }
        }
    
    async def generate_comprehensive_framework(
        self,
        requirements: Dict[str, Any],
        user_message: str = "",
        framework_template: str = "standard",
        enable_literature_search: bool = True
    ) -> Dict[str, Any]:
        """
        生成完整的方案框架
        集成所有核心功能模块
        """
        try:
            logger.info(f"🚀 启动综合方案框架生成 - 模板: {framework_template}")
            
            # 验证模板
            if framework_template not in self.framework_templates:
                framework_template = "standard"
            
            template_config = self.framework_templates[framework_template]
            components = template_config["components"]
            
            # 核心功能模块并行生成
            framework_data = {}
            
            # 1. 研究意图分析和精准搜索链接 (核心功能)
            logger.info("🔍 生成研究意图和精准搜索链接")
            research_intent_result = await self.research_intent_service.analyze_research_intent_and_generate_keywords(
                requirements, user_message
            )
            framework_data["research_intent_analysis"] = research_intent_result
            
            # 2. 方案概览卡片
            if "solution_overview" in components:
                logger.info("📋 生成方案概览")
                solution_overview = await self._generate_solution_overview(
                    requirements, research_intent_result["intent_analysis"]
                )
                framework_data["solution_overview"] = solution_overview

            # 3. 风险评估（调整为积极的、促成交易的风险分析）
            if "risk_assessment" in components:
                logger.info("⚠️ 生成风险评估")
                risk_assessment = await self._generate_positive_risk_assessment(
                    requirements, research_intent_result["intent_analysis"]
                )
                framework_data["risk_assessment"] = risk_assessment

            # 4. 研究建议（新增模块）
            if "research_recommendations" in components:
                logger.info("💡 生成研究建议")
                research_recommendations = await self._generate_research_recommendations(
                    requirements, research_intent_result["intent_analysis"]
                )
                framework_data["research_recommendations"] = research_recommendations

            # 5. 文献推荐（合并精准搜索和文献推荐功能）
            if "literature_recommendations" in components and enable_literature_search:
                logger.info("📚 生成文献推荐")
                literature_recommendations = await self._generate_enhanced_literature_recommendations(
                    requirements, research_intent_result["intent_analysis"]
                )
                framework_data["literature_recommendations"] = literature_recommendations
            
            # 7. 项目实施规划
            if "implementation_plan" in components:
                logger.info("📅 生成实施规划")
                implementation_plan = self._generate_implementation_plan(
                    requirements, research_intent_result["intent_analysis"]
                )
                framework_data["implementation_plan"] = implementation_plan
            
            # 8. 基础推荐 (简化版)
            if "basic_recommendations" in components:
                logger.info("💡 生成基础推荐")
                basic_recommendations = self._generate_basic_recommendations(
                    requirements, research_intent_result["intent_analysis"]
                )
                framework_data["basic_recommendations"] = basic_recommendations
            
            # 整合最终方案框架
            comprehensive_framework = self._assemble_comprehensive_framework(
                framework_data, requirements, template_config
            )
            
            logger.info("✅ 综合方案框架生成完成")
            return comprehensive_framework
            
        except Exception as e:
            logger.error(f"综合方案框架生成失败: {e}")
            return self._get_fallback_framework(requirements, framework_template)
    
    async def _generate_solution_overview(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成方案概览卡片"""
        
        research_domain = intent_analysis.get("research_domain", "general")
        sample_type = requirements.get("sampleType", "")
        budget = requirements.get("budget", "")
        
        # 基于研究领域推荐平台
        recommended_platform = self._get_recommended_platform(research_domain, sample_type, budget)
        
        # 估算成本
        estimated_cost = self._estimate_solution_cost(requirements, recommended_platform)
        
        # 风险等级评估
        risk_level = self._assess_risk_level(requirements, intent_analysis)
        
        # 置信度计算
        confidence = intent_analysis.get("confidence", 0.8)
        
        overview = {
            "research_type": f"{research_domain.title()} 研究 - 单细胞测序",
            "estimated_cost": estimated_cost,
            "recommended_platform": recommended_platform["name"],
            "risk_level": risk_level["level"],
            "confidence": confidence,
            "solution_highlights": [
                f"专门针对{research_domain}领域优化的解决方案",
                f"基于{sample_type}样本的专业化处理流程",
                f"集成智能文献搜索和精准关键词生成",
                f"提供一键直达的多平台搜索链接"
            ],
            "key_advantages": recommended_platform.get("advantages", []),
            "expected_timeline": self._estimate_timeline(requirements),
            "quality_assurance": [
                "AI驱动的研究意图分析",
                "多平台文献搜索支持",
                "智能风险评估和缓解",
                "个性化方案定制"
            ]
        }
        
        return overview

    async def _generate_positive_risk_assessment(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成积极的、促成交易的风险评估"""

        budget = requirements.get("budget", "")
        timeline = requirements.get("timeline", "")
        urgency = requirements.get("urgencyLevel", "")
        sample_type = requirements.get("sampleType", "")
        cell_count = requirements.get("cellCount", "")

        # 积极的风险分析框架
        risk_assessment = {
            "overall_feasibility": "高度可行",
            "success_probability": "85-95%",
            "risk_level": "可控风险",
            "client_advantages": [
                "项目需求明确，技术路线成熟",
                "预算范围合理，能够支持高质量实验",
                "时间规划适中，有充分的质量保证空间",
                "样本条件良好，成功率有保障"
            ],
            "manageable_risks": {
                "technical_risks": {
                    "level": "低风险",
                    "description": "单细胞测序技术已非常成熟，成功率高",
                    "mitigation": "采用经验证的标准流程，配备专业技术团队",
                    "client_benefit": "技术风险低意味着结果可靠性高，投资回报有保障"
                },
                "timeline_risks": {
                    "level": "可控",
                    "description": f"基于{timeline}的时间安排是合理的",
                    "mitigation": "制定详细的项目时间表，设置关键节点监控",
                    "client_benefit": "合理的时间安排确保实验质量，避免匆忙导致的错误"
                },
                "cost_risks": {
                    "level": "透明可控",
                    "description": f"{budget}预算范围能够支持高质量实验",
                    "mitigation": "提供详细的成本分解，无隐藏费用",
                    "client_benefit": "预算透明，性价比高，避免后期追加成本"
                },
                "sample_risks": {
                    "level": "风险极低",
                    "description": f"{sample_type}样本处理技术成熟",
                    "mitigation": "采用标准化样本处理流程，质控严格",
                    "client_benefit": "样本利用率高，减少重复实验的可能性"
                }
            },
            "success_factors": [
                "客户需求清晰明确，便于精准执行",
                "预算充足，能够选择最优技术方案",
                "时间安排合理，有利于质量控制",
                "团队经验丰富，技术实力有保障"
            ],
            "value_proposition": {
                "immediate_benefits": [
                    "快速获得高质量的单细胞测序数据",
                    "专业的数据分析和解读服务",
                    "完整的技术支持和售后服务"
                ],
                "long_term_value": [
                    "为后续研究奠定坚实的数据基础",
                    "提升研究成果的发表竞争力",
                    "建立长期的技术合作关系"
                ]
            },
            "confidence_statement": "基于我们丰富的项目经验和成熟的技术平台，该项目具有很高的成功概率。风险因素都在可控范围内，且有相应的应对措施。"
        }

        return risk_assessment

    async def _generate_research_recommendations(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成研究建议模块"""

        research_domain = intent_analysis.get("research_domain", "general")
        research_goal = requirements.get("researchGoal", "")
        sample_type = requirements.get("sampleType", "")
        experiment_type = requirements.get("experimentType", "")

        # 基于研究领域的专业建议
        domain_specific_recommendations = {
            "cancer": {
                "key_considerations": [
                    "肿瘤异质性分析是关键重点",
                    "免疫微环境的细胞组成分析",
                    "肿瘤干细胞标记物的识别",
                    "药物靶点相关基因的表达分析"
                ],
                "experimental_design": [
                    "建议包含正常对照样本进行比较",
                    "考虑不同肿瘤分期的样本",
                    "关注免疫细胞浸润情况",
                    "分析细胞周期和凋亡相关基因"
                ],
                "analysis_focus": [
                    "差异表达基因分析",
                    "细胞轨迹分析",
                    "细胞通讯网络分析",
                    "功能富集分析"
                ]
            },
            "immunology": {
                "key_considerations": [
                    "免疫细胞亚群的精确分类",
                    "激活状态和功能标记物分析",
                    "细胞因子和趋化因子表达",
                    "T细胞受体多样性分析"
                ],
                "experimental_design": [
                    "包含刺激和未刺激条件",
                    "考虑时间序列采样",
                    "多个免疫器官的比较",
                    "疾病状态与健康对照比较"
                ],
                "analysis_focus": [
                    "免疫细胞分型和功能分析",
                    "克隆扩增分析",
                    "免疫应答通路分析",
                    "细胞间相互作用分析"
                ]
            },
            "neuroscience": {
                "key_considerations": [
                    "神经元和胶质细胞的分类",
                    "神经发育和可塑性相关基因",
                    "神经递质系统的分析",
                    "疾病相关的神经退行性变化"
                ],
                "experimental_design": [
                    "不同脑区的比较分析",
                    "发育时间点的选择",
                    "疾病模型与对照的比较",
                    "行为学实验的结合"
                ],
                "analysis_focus": [
                    "神经元亚型鉴定",
                    "发育轨迹分析",
                    "神经环路连接分析",
                    "疾病机制探索"
                ]
            },
            "development": {
                "key_considerations": [
                    "发育时间序列的设计",
                    "干细胞分化轨迹追踪",
                    "形态发生相关基因分析",
                    "细胞命运决定机制"
                ],
                "experimental_design": [
                    "多个发育时间点采样",
                    "空间位置信息的保留",
                    "谱系追踪实验的结合",
                    "功能验证实验的规划"
                ],
                "analysis_focus": [
                    "伪时间轨迹分析",
                    "转录因子调控网络",
                    "细胞分化路径分析",
                    "发育关键基因识别"
                ]
            }
        }

        # 获取领域特异性建议
        domain_recommendations = domain_specific_recommendations.get(
            research_domain, domain_specific_recommendations["cancer"]
        )

        # 通用研究建议
        research_recommendations = {
            "experimental_strategy": {
                "primary_objectives": [
                    f"针对{research_goal}的深入分析",
                    f"基于{sample_type}的细胞类型鉴定",
                    "高质量数据的获取和分析",
                    "生物学意义的深度挖掘"
                ],
                "recommended_approach": domain_recommendations["experimental_design"],
                "quality_control": [
                    "严格的样本质量控制",
                    "细胞活力和完整性检测",
                    "测序深度和覆盖度优化",
                    "批次效应的控制和校正"
                ]
            },
            "data_analysis_strategy": {
                "core_analyses": domain_recommendations["analysis_focus"],
                "advanced_analyses": [
                    "单细胞调控网络分析",
                    "细胞间通讯分析",
                    "功能富集和通路分析",
                    "与公共数据库的整合分析"
                ],
                "visualization_recommendations": [
                    "t-SNE/UMAP降维可视化",
                    "热图和小提琴图展示",
                    "轨迹分析可视化",
                    "网络图和通路图"
                ]
            },
            "success_optimization": {
                "critical_factors": domain_recommendations["key_considerations"],
                "best_practices": [
                    "充分的文献调研和实验设计",
                    "合适的对照组设置",
                    "标准化的实验流程",
                    "专业的数据分析团队"
                ],
                "expected_outcomes": [
                    "高质量的单细胞转录组数据",
                    "清晰的细胞类型分类结果",
                    "生物学功能的深入理解",
                    "可发表的科研成果"
                ]
            },
            "follow_up_suggestions": [
                "基于结果设计功能验证实验",
                "考虑多组学数据的整合分析",
                "与临床数据的关联分析",
                "后续深入机制研究的规划"
            ]
        }

        return research_recommendations

    async def _generate_enhanced_literature_recommendations(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成增强的文献推荐（合并精准搜索和文献推荐功能）"""

        try:
            # 调用研究意图服务获取精准搜索链接
            research_intent_service = get_research_intent_service()
            search_strategy = await research_intent_service.generate_integrated_search_strategy(
                requirements, ""
            )

            # 生成文献推荐
            literature_service = get_literature_service()
            literature_results = await literature_service.get_literature_recommendations(
                requirements, ""
            )

            # 合并精准搜索和文献推荐
            enhanced_literature = {
                "precision_search_links": {
                    "description": "基于AI分析生成的精准搜索链接，使用英文关键词确保国际期刊检索效果",
                    "primary_search": {
                        "query": search_strategy.get("primary_query", ""),
                        "platforms": {
                            "pubmed": f"https://pubmed.ncbi.nlm.nih.gov/?term={search_strategy.get('primary_query', '').replace(' ', '+')}&sort=date",
                            "google_scholar": f"https://scholar.google.com/scholar?q={search_strategy.get('primary_query', '').replace(' ', '+')}&hl=en&as_sdt=0%2C5&as_ylo=2020",
                            "web_of_science": f"https://www.webofscience.com/wos/woscc/basic-search?search-mode=general&search-text={search_strategy.get('primary_query', '').replace(' ', '+')}"
                        }
                    },
                    "alternative_searches": []
                },
                "curated_literature": {
                    "core_papers": literature_results.get("core_papers", []),
                    "recent_advances": literature_results.get("recent_papers", []),
                    "methodology_papers": literature_results.get("methodology_papers", []),
                    "review_articles": literature_results.get("review_papers", [])
                },
                "search_optimization": {
                    "english_keywords": search_strategy.get("suggested_terms", []),
                    "research_focus": search_strategy.get("research_focus", ""),
                    "search_tips": [
                        "使用英文关键词进行搜索以获得更全面的结果",
                        "结合多个数据库进行交叉验证",
                        "关注近3年的最新研究进展",
                        "重点关注高影响因子期刊的相关文章"
                    ]
                },
                "literature_analysis": {
                    "trending_topics": [
                        "Single-cell multiomics integration",
                        "Spatial transcriptomics applications",
                        "Cell-cell communication analysis",
                        "Machine learning in single-cell analysis"
                    ],
                    "key_methodologies": [
                        "10x Genomics Chromium platform",
                        "Smart-seq2 protocol",
                        "Droplet-based sequencing",
                        "Plate-based single-cell sequencing"
                    ],
                    "research_gaps": [
                        "标准化数据分析流程的建立",
                        "批次效应校正方法的优化",
                        "罕见细胞类型的识别技术",
                        "多组学数据整合分析方法"
                    ]
                }
            }

            # 添加备选搜索查询
            for alt_query in search_strategy.get("alternative_queries", [])[:3]:
                if alt_query:
                    enhanced_literature["precision_search_links"]["alternative_searches"].append({
                        "query": alt_query,
                        "pubmed_link": f"https://pubmed.ncbi.nlm.nih.gov/?term={alt_query.replace(' ', '+')}&sort=date",
                        "scholar_link": f"https://scholar.google.com/scholar?q={alt_query.replace(' ', '+')}&hl=en&as_sdt=0%2C5&as_ylo=2020"
                    })

            return enhanced_literature

        except Exception as e:
            logger.error(f"生成增强文献推荐失败: {e}")
            # 返回基础的文献推荐结构
            return {
                "precision_search_links": {
                    "description": "精准搜索功能暂时不可用，建议手动搜索相关文献",
                    "primary_search": {
                        "query": "single cell RNA sequencing",
                        "platforms": {
                            "pubmed": "https://pubmed.ncbi.nlm.nih.gov/?term=single+cell+RNA+sequencing&sort=date",
                            "google_scholar": "https://scholar.google.com/scholar?q=single+cell+RNA+sequencing&hl=en"
                        }
                    }
                },
                "curated_literature": {
                    "core_papers": [],
                    "recent_advances": [],
                    "methodology_papers": [],
                    "review_articles": []
                },
                "search_optimization": {
                    "english_keywords": ["single cell", "RNA sequencing", "transcriptomics"],
                    "search_tips": [
                        "使用英文关键词进行搜索",
                        "关注近期发表的高质量文献"
                    ]
                }
            }
    
    def _get_recommended_platform(
        self,
        research_domain: str,
        sample_type: str,
        budget: str
    ) -> Dict[str, Any]:
        """基于研究领域智能推荐平台"""
        
        # 领域特异性平台推荐
        domain_platforms = {
            "cancer": {
                "name": "10x Genomics + Spatial Transcriptomics",
                "reason": "肿瘤异质性和微环境空间信息分析",
                "advantages": [
                    "高精度肿瘤细胞检测",
                    "空间位置信息保留",
                    "免疫浸润精确定量",
                    "支持FFPE样本处理"
                ]
            },
            "immunology": {
                "name": "10x Genomics + Feature Barcoding",
                "reason": "免疫细胞表型和功能状态综合分析",
                "advantages": [
                    "RNA+蛋白质双重检测",
                    "T/B细胞受体测序",
                    "免疫细胞亚群精细分析",
                    "激活状态实时监测"
                ]
            },
            "neuroscience": {
                "name": "10x Genomics + Spatial",
                "reason": "神经网络连接和空间组织分析",
                "advantages": [
                    "神经元类型精确分类",
                    "脑区特异性分析",
                    "神经连接网络重建",
                    "疾病相关通路识别"
                ]
            },
            "development": {
                "name": "10x Genomics + RNA Velocity",
                "reason": "发育轨迹和细胞命运分析",
                "advantages": [
                    "时间序列轨迹重建",
                    "细胞状态转换预测",
                    "关键调控节点识别",
                    "发育异常检测"
                ]
            },
            "general": {
                "name": "10x Genomics Chromium",
                "reason": "通用单细胞转录组分析平台",
                "advantages": [
                    "技术成熟稳定",
                    "数据质量可靠",
                    "分析工具完善",
                    "文献支持丰富"
                ]
            }
        }
        
        # 预算调整
        if "5万以下" in budget:
            return {
                "name": "Smart-seq3 (经济型)",
                "reason": "预算优化的高质量分析方案",
                "advantages": [
                    "成本效益优化",
                    "全长转录本检测",
                    "适合少量样本",
                    "数据质量保证"
                ]
            }
        
        return domain_platforms.get(research_domain, domain_platforms["general"])
    
    def _estimate_solution_cost(
        self,
        requirements: Dict[str, Any],
        recommended_platform: Dict[str, Any]
    ) -> str:
        """估算解决方案成本"""
        
        sample_count = self._extract_sample_count(requirements.get("sampleCount", "1个样本"))
        budget_range = requirements.get("budget", "")
        
        # 基础成本计算
        if "Smart-seq" in recommended_platform["name"]:
            base_cost_per_sample = 1200
        elif "Spatial" in recommended_platform["name"]:
            base_cost_per_sample = 2500
        elif "Feature Barcoding" in recommended_platform["name"]:
            base_cost_per_sample = 2000
        else:
            base_cost_per_sample = 1500
        
        total_cost = base_cost_per_sample * sample_count
        
        # 服务费用
        service_cost = total_cost * 0.3
        
        # 分析费用
        analysis_cost = sample_count * 800
        
        final_cost = total_cost + service_cost + analysis_cost
        
        return f"¥{int(final_cost):,} ({sample_count}个样本)"
    
    def _assess_risk_level(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """评估整体风险等级"""
        
        risk_factors = []
        risk_score = 0
        
        # 样本复杂度风险
        sample_type = requirements.get("sampleType", "").lower()
        if "ffpe" in sample_type or "fixed" in sample_type:
            risk_factors.append("FFPE样本RNA质量风险")
            risk_score += 2
        elif "tumor" in sample_type or "肿瘤" in sample_type:
            risk_factors.append("肿瘤样本异质性高")
            risk_score += 1
        
        # 时间压力风险
        timeline = requirements.get("timeline", "")
        if "1个月" in timeline or "紧急" in timeline:
            risk_factors.append("时间紧迫影响质量控制")
            risk_score += 2
        
        # 预算限制风险
        budget = requirements.get("budget", "")
        if "5万以下" in budget:
            risk_factors.append("预算限制影响技术选择")
            risk_score += 1
        
        # AI分析置信度风险
        confidence = intent_analysis.get("confidence", 0.8)
        if confidence < 0.7:
            risk_factors.append("需求理解不够清晰")
            risk_score += 1
        
        # 风险等级确定
        if risk_score <= 1:
            risk_level = "low"
            risk_color = "green"
            success_rate = "90-95%"
        elif risk_score <= 3:
            risk_level = "medium"
            risk_color = "yellow"
            success_rate = "80-90%"
        else:
            risk_level = "high"
            risk_color = "red"
            success_rate = "70-80%"
        
        return {
            "level": risk_level,
            "color": risk_color,
            "score": risk_score,
            "factors": risk_factors,
            "success_rate": success_rate,
            "mitigation_ready": len(risk_factors) > 0
        }
    
    def _estimate_timeline(self, requirements: Dict[str, Any]) -> str:
        """估算项目时间"""
        timeline_req = requirements.get("timeline", "")
        sample_count = self._extract_sample_count(requirements.get("sampleCount", "1个样本"))
        
        if "1个月" in timeline_req:
            return "4周 (紧急流程)"
        elif sample_count > 5:
            return "6-8周 (多样本处理)"
        else:
            return "4-6周 (标准流程)"
    
    async def _generate_key_factors_analysis(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成关键要素分析"""
        
        research_domain = intent_analysis.get("research_domain", "general")
        
        # 成功关键因素
        success_factors = []
        if research_domain == "cancer":
            success_factors = [
                "肿瘤样本质量和细胞活力保证",
                "空间信息保留和细胞位置追踪",
                "免疫浸润细胞的准确识别",
                "肿瘤异质性的全面分析"
            ]
        elif research_domain == "immunology":
            success_factors = [
                "免疫细胞亚群的精确分类",
                "T/B细胞受体序列的完整获取",
                "激活状态标记物的准确检测",
                "细胞间相互作用的识别"
            ]
        else:
            success_factors = [
                "高质量单细胞数据的获得",
                "准确的细胞类型注释",
                "可靠的生物学结论",
                "可重现的实验结果"
            ]
        
        # 风险评估
        risk_factors = await self._analyze_project_risks(requirements, intent_analysis)
        
        return {
            "success_factors": success_factors,
            "risk_factors": risk_factors,
            "critical_milestones": [
                "样本质量验证通过",
                "单细胞捕获效率达标",
                "测序数据质量确认",
                "分析结果生物学验证"
            ],
            "quality_metrics": [
                "细胞捕获率 >70%",
                "基因检出数 >2000/cell",
                "双细胞率 <5%",
                "批次效应 <10%"
            ]
        }
    
    async def _generate_literature_recommendations(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成智能文献推荐"""
        
        # 使用增强方案服务的文献推荐功能
        enhanced_recommendations = await self.enhanced_solution_service.generate_comprehensive_solution(
            requirements, intent_analysis
        )
        
        literature_section = enhanced_recommendations.get("literature_recommendations", {})
        
        # 增强文献推荐
        research_domain = intent_analysis.get("research_domain", "general")
        
        # 添加领域特异性的重点文献
        domain_key_papers = self._get_domain_key_papers(research_domain)
        
        enhanced_literature = {
            "strategy": f"基于{research_domain}领域和研究意图的智能文献推荐",
            "key_papers": domain_key_papers,
            "methodological_papers": [
                {
                    "title": "Best practices for single-cell analysis across modalities",
                    "authors": ["Luecken, M.D.", "Theis, F.J."],
                    "journal": "Nature Reviews Genetics",
                    "year": 2019,
                    "relevance": "单细胞分析方法学指南",
                    "priority": "high"
                }
            ],
            "recent_advances": [
                {
                    "title": f"Recent advances in {research_domain} single-cell genomics",
                    "focus": "最新技术发展和应用案例",
                    "time_range": "2023-2024",
                    "search_ready": True
                }
            ],
            "reading_guide": {
                "priority_order": ["方法学文献", "领域关键论文", "最新进展"],
                "time_allocation": "建议总阅读时间: 4-6小时",
                "focus_sections": ["Materials and Methods", "Results", "Discussion"]
            }
        }
        
        return enhanced_literature
    
    def _get_domain_key_papers(self, research_domain: str) -> List[Dict[str, Any]]:
        """获取领域关键文献"""
        
        domain_papers = {
            "cancer": [
                {
                    "title": "Single-cell RNA-seq reveals new types of human blood dendritic cells",
                    "focus": "癌症免疫微环境中的树突状细胞",
                    "significance": "肿瘤免疫治疗关键参考"
                }
            ],
            "immunology": [
                {
                    "title": "Single-cell map of diverse immune phenotypes in the breast tumor microenvironment",
                    "focus": "肿瘤微环境免疫细胞图谱",
                    "significance": "免疫治疗靶点发现"
                }
            ],
            "neuroscience": [
                {
                    "title": "Single-cell transcriptomic atlas of the human retina",
                    "focus": "视网膜单细胞转录组图谱",
                    "significance": "神经退行性疾病研究"
                }
            ],
            "development": [
                {
                    "title": "Single-cell reconstruction of developmental trajectories",
                    "focus": "发育轨迹重建方法学",
                    "significance": "发育生物学经典文献"
                }
            ]
        }
        
        return domain_papers.get(research_domain, [])
    
    def _generate_platform_comparison(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成技术平台对比分析"""
        
        research_domain = intent_analysis.get("research_domain", "general")
        budget = requirements.get("budget", "")
        
        # 平台对比矩阵
        platforms = [
            {
                "name": "10x Genomics",
                "throughput": "高 (10K cells)",
                "cost": "中等 (¥1,500/样本)",
                "data_quality": "优秀",
                "适用场景": ["标准单细胞分析", "大规模筛选"],
                "优势": ["技术成熟", "数据质量高", "分析工具完善"],
                "劣势": ["成本较高", "3'端偏向性"],
                "推荐度": "★★★★★" if research_domain != "development" else "★★★★"
            },
            {
                "name": "Smart-seq3",
                "throughput": "中等 (384 cells)",
                "cost": "低 (¥800/样本)",
                "data_quality": "优秀",
                "适用场景": ["全长转录本", "预算限制"],
                "优势": ["全长覆盖", "成本低", "灵敏度高"],
                "劣势": ["通量相对低", "操作复杂"],
                "推荐度": "★★★★" if "5万以下" in budget else "★★★"
            }
        ]
        
        # 基于研域添加特殊平台
        if research_domain == "cancer":
            platforms.append({
                "name": "Spatial Transcriptomics",
                "throughput": "中等 (5K spots)",
                "cost": "高 (¥2,500/样本)",
                "data_quality": "优秀",
                "适用场景": ["肿瘤微环境", "空间异质性"],
                "优势": ["保留空间信息", "微环境分析"],
                "劣势": ["成本高", "分辨率限制"],
                "推荐度": "★★★★★"
            })
        
        return {
            "comparison_matrix": platforms,
            "selection_criteria": [
                "研究目标匹配度",
                "预算适配性",
                "技术成熟度",
                "数据质量要求"
            ],
            "recommendation_summary": self._generate_platform_recommendation_summary(
                platforms, research_domain, budget
            )
        }
    
    def _generate_platform_recommendation_summary(
        self,
        platforms: List[Dict[str, Any]],
        research_domain: str,
        budget: str
    ) -> Dict[str, Any]:
        """生成平台推荐总结"""
        
        # 找出最高推荐度的平台
        best_platform = max(platforms, key=lambda x: len(x["推荐度"]))
        
        return {
            "top_recommendation": best_platform["name"],
            "reason": f"基于{research_domain}研究需求和预算考虑的最佳选择",
            "key_benefits": best_platform["优势"],
            "considerations": best_platform["劣势"],
            "alternative_options": [p["name"] for p in platforms if p != best_platform][:2]
        }
    
    async def _generate_risk_assessment_matrix(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成风险评估矩阵"""
        
        # 使用增强方案服务的风险评估
        enhanced_solution = await self.enhanced_solution_service.generate_comprehensive_solution(
            requirements, intent_analysis
        )
        
        base_risk_assessment = enhanced_solution.get("risk_assessment", {})
        
        # 增强风险矩阵
        risk_matrix = {
            "overall_assessment": base_risk_assessment,
            "risk_categories": {
                "技术风险": {
                    "level": "中等",
                    "factors": ["平台选择", "数据质量", "分析复杂度"],
                    "mitigation": ["多重验证", "质控标准", "专家支持"],
                    "impact": "可能影响数据质量和分析结果"
                },
                "时间风险": {
                    "level": "低" if "标准" in requirements.get("timeline", "") else "中等",
                    "factors": ["样本处理时间", "测序排期", "分析周期"],
                    "mitigation": ["提前规划", "备用方案", "并行处理"],
                    "impact": "可能延误项目交付时间"
                },
                "成本风险": {
                    "level": "低" if "充足" in requirements.get("budget", "") else "中等",
                    "factors": ["技术选择", "样本数量", "分析深度"],
                    "mitigation": ["预算控制", "分阶段实施", "成本优化"],
                    "impact": "可能超出预算范围"
                }
            },
            "contingency_plans": [
                "备用技术平台方案",
                "样本质量救援措施",
                "数据分析多重策略",
                "专家紧急支持"
            ],
            "success_probability": "85-92%"
        }
        
        return risk_matrix
    
    def _generate_implementation_plan(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成项目实施规划"""
        
        timeline = requirements.get("timeline", "标准")
        sample_count = self._extract_sample_count(requirements.get("sampleCount", "1个样本"))
        
        # 基于时间要求调整计划
        if "1个月" in timeline:
            phases = {
                "第1周": {
                    "tasks": ["需求确认", "方案设计", "样本接收"],
                    "deliverables": ["技术方案书", "项目计划"],
                    "milestones": ["样本质检通过"]
                },
                "第2周": {
                    "tasks": ["样本制备", "单细胞处理", "文库构建"],
                    "deliverables": ["样本质控报告", "文库质检"],
                    "milestones": ["文库质量达标"]
                },
                "第3周": {
                    "tasks": ["高通量测序", "数据产出", "初步质控"],
                    "deliverables": ["原始数据", "测序报告"],
                    "milestones": ["测序完成"]
                },
                "第4周": {
                    "tasks": ["数据分析", "结果验证", "报告撰写"],
                    "deliverables": ["分析报告", "可视化图表"],
                    "milestones": ["项目交付"]
                }
            }
        else:
            phases = {
                "第1-2周": {
                    "tasks": ["深度需求分析", "方案优化", "样本预处理"],
                    "deliverables": ["详细技术方案", "样本处理协议"],
                    "milestones": ["方案最终确认"]
                },
                "第3-4周": {
                    "tasks": ["样本制备优化", "质量控制", "文库构建"],
                    "deliverables": ["样本质控数据", "文库质量报告"],
                    "milestones": ["样本处理完成"]
                },
                "第5-6周": {
                    "tasks": ["测序执行", "数据质控", "初步分析"],
                    "deliverables": ["测序数据", "数据质量报告"],
                    "milestones": ["数据产出完成"]
                },
                "第7-8周": {
                    "tasks": ["深度分析", "结果验证", "报告完善"],
                    "deliverables": ["完整分析报告", "结果解读"],
                    "milestones": ["项目完结"]
                }
            }
        
        return {
            "project_phases": phases,
            "critical_path": [
                "样本质量确认",
                "文库构建成功",
                "测序数据达标",
                "分析结果验证"
            ],
            "resource_allocation": {
                "技术团队": "2-3人专项团队",
                "设备资源": "优先设备使用权",
                "时间分配": f"总计{len(phases)}个阶段，{timeline}"
            },
            "quality_checkpoints": [
                "每周进度检查",
                "关键节点验收",
                "结果质量审查",
                "客户确认节点"
            ]
        }
    
    def _generate_basic_recommendations(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成基础推荐（简化版）"""
        
        research_domain = intent_analysis.get("research_domain", "general")
        
        return {
            "quick_recommendations": [
                f"推荐使用10x Genomics平台进行{research_domain}研究",
                "建议采用标准单细胞RNA测序流程",
                "预计4-6周完成项目",
                "预算范围: ¥8,000-15,000"
            ],
            "next_steps": [
                "确认研究需求和样本信息",
                "选择合适的技术方案",
                "安排样本处理时间",
                "启动项目执行"
            ],
            "key_benefits": [
                "快速获得高质量数据",
                "专业技术团队支持",
                "完整的分析报告",
                "后续技术咨询"
            ]
        }
    
    async def _analyze_project_risks(
        self,
        requirements: Dict[str, Any],
        intent_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """分析项目风险因素"""
        
        risks = []
        
        # 样本相关风险
        sample_type = requirements.get("sampleType", "").lower()
        if "ffpe" in sample_type:
            risks.append({
                "type": "样本风险",
                "description": "FFPE样本RNA降解风险",
                "probability": "中等",
                "impact": "高",
                "mitigation": "使用FFPE专用试剂盒和优化流程"
            })
        
        # 技术风险
        if intent_analysis.get("confidence", 0.8) < 0.7:
            risks.append({
                "type": "技术风险",
                "description": "需求理解不够清晰可能影响方案设计",
                "probability": "低",
                "impact": "中等",
                "mitigation": "增加需求澄清和专家咨询"
            })
        
        # 时间风险
        timeline = requirements.get("timeline", "")
        if "1个月" in timeline:
            risks.append({
                "type": "时间风险",
                "description": "紧急时间要求可能影响质量控制",
                "probability": "中等",
                "impact": "中等",
                "mitigation": "采用快速流程但保持关键质控点"
            })
        
        return risks
    
    def _assemble_comprehensive_framework(
        self,
        framework_data: Dict[str, Any],
        requirements: Dict[str, Any],
        template_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """整合最终的综合方案框架"""
        
        comprehensive_framework = {
            "framework_id": f"comprehensive_{int(datetime.now().timestamp())}",
            "generated_at": datetime.now().isoformat(),
            "template_used": template_config["name"],
            "complexity_level": template_config["complexity"],
            "target_users": template_config["target_users"],
            
            # 核心组件
            "research_intent_analysis": framework_data.get("research_intent_analysis", {}),
            
            # 可选组件（基于模板）
            **{k: v for k, v in framework_data.items() if k != "research_intent_analysis"},
            
            # 元数据
            "framework_metadata": {
                "components_included": list(framework_data.keys()),
                "generation_method": "comprehensive_ai_framework",
                "optimization_level": "high",
                "user_experience_focus": "modern_interactive",
                "customization_level": template_config["complexity"]
            },
            
            # 用户交互指南
            "user_guide": {
                "how_to_use": [
                    "查看方案概览了解核心推荐",
                    "使用精准搜索链接获取相关文献",
                    "参考风险评估制定应对策略",
                    "按实施计划推进项目执行"
                ],
                "interactive_features": [
                    "一键文献搜索链接",
                    "动态风险评估",
                    "个性化推荐",
                    "实时进度跟踪"
                ]
            },
            
            # 质量保证
            "quality_assurance": {
                "ai_confidence": framework_data.get("research_intent_analysis", {}).get("generation_metadata", {}).get("confidence_score", 0.85),
                "validation_checks": "通过多重验证",
                "expert_review": "专家团队审核",
                "continuous_improvement": "基于用户反馈持续优化"
            }
        }
        
        return comprehensive_framework
    
    def _extract_sample_count(self, sample_count_str: str) -> int:
        """提取样本数量"""
        if "1个" in sample_count_str:
            return 1
        elif "2-3个" in sample_count_str:
            return 3
        elif "4-5个" in sample_count_str:
            return 5
        elif "6-10个" in sample_count_str:
            return 8
        elif "10个以上" in sample_count_str:
            return 12
        else:
            return 3
    
    def _get_fallback_framework(
        self,
        requirements: Dict[str, Any],
        framework_template: str
    ) -> Dict[str, Any]:
        """降级方案框架"""
        
        return {
            "framework_id": f"fallback_{int(datetime.now().timestamp())}",
            "status": "fallback_mode",
            "template_used": framework_template,
            "message": "系统生成基础框架，建议联系专家获取详细方案",
            
            "basic_framework": {
                "recommended_approach": "10x Genomics标准流程",
                "estimated_cost": "¥10,000-20,000",
                "timeline": "4-6周",
                "risk_level": "中等"
            },
            
            "basic_search_links": {
                "pubmed": f"https://pubmed.ncbi.nlm.nih.gov/?term=single+cell+RNA+sequencing",
                "google_scholar": f"https://scholar.google.com/scholar?q=single+cell+transcriptomics"
            },
            
            "next_steps": [
                "联系技术专家详细咨询",
                "提供更具体的需求信息",
                "制定详细的项目方案"
            ]
        }


# 全局服务实例
comprehensive_solution_service = ComprehensiveSolutionService()


def get_comprehensive_solution_service() -> ComprehensiveSolutionService:
    """获取综合解决方案框架服务实例"""
    return comprehensive_solution_service