"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/conversation-interface.tsx":
/*!***********************************************!*\
  !*** ./components/conversation-interface.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationInterface: () => (/* binding */ ConversationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/enhanced-error-handler */ \"(app-pages-browser)/./lib/enhanced-error-handler.ts\");\n/* harmony import */ var _enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./enhanced-loading-indicator */ \"(app-pages-browser)/./components/enhanced-loading-indicator.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _progressive_requirement_collector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./progressive-requirement-collector */ \"(app-pages-browser)/./components/progressive-requirement-collector.tsx\");\n/* harmony import */ var _formatted_message__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./formatted-message */ \"(app-pages-browser)/./components/formatted-message.tsx\");\n/* harmony import */ var _conversation_history__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./conversation-history */ \"(app-pages-browser)/./components/conversation-history.tsx\");\n/* harmony import */ var _literature_search_toggle__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./literature-search-toggle */ \"(app-pages-browser)/./components/literature-search-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConversationInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ConversationInterface() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [conversationId, setConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [requirements, setRequirements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rightPanelCollapsed, setRightPanelCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resetTrigger, setResetTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 用于触发需求收集助手重置\n    ;\n    const [enableLiteratureSearch, setEnableLiteratureSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 文献搜索开关\n    ;\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // 使用增强的加载状态管理\n    const { isLoading, currentStage, progress, startLoading, updateStage, finishLoading, cancelLoading } = (0,_enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.useStageLoading)();\n    // 响应式检测\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ConversationInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 1024);\n                    if (window.innerWidth < 1024) {\n                        setRightPanelCollapsed(true);\n                    }\n                }\n            }[\"ConversationInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ConversationInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ConversationInterface.useEffect\"];\n        }\n    }[\"ConversationInterface.useEffect\"], []);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ConversationInterface.useEffect\"], [\n        messages\n    ]);\n    // 初始化欢迎消息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            const welcomeMessage = {\n                id: \"welcome\",\n                type: \"ai\",\n                content: \"您好\".concat((user === null || user === void 0 ? void 0 : user.name) ? \"，\".concat(user.name) : '', \"！\\uD83D\\uDC4B\\n\\n我是 **CellForge AI** 智能顾问，专注于单细胞测序技术咨询。\\n\\n\\uD83D\\uDD2C **我的专长**：\\n• 单细胞RNA测序 (scRNA-seq)\\n• 单细胞ATAC测序 (scATAC-seq)\\n• 多组学测序 (Multiome)\\n• 空间转录组学\\n• 实验设计与数据分析\\n\\n\\uD83D\\uDCA1 **开始方式**：\\n您可以直接提问，我会智能收集您的需求信息，或者使用右侧的需求收集助手快速填写项目信息。\\n\\n请告诉我您的研究目标，我来为您制定最佳的技术方案！\"),\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: 1.0,\n                suggestions: [\n                    \"我需要进行单细胞RNA测序\",\n                    \"请推荐适合的技术平台\",\n                    \"帮我分析项目成本\",\n                    \"查看成功案例\"\n                ]\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n        }\n    }[\"ConversationInterface.useEffect\"], [\n        user\n    ]);\n    // 自动保存对话到历史\n    const autoSaveConversation = (messages)=>{\n        if (messages.length >= 2) {\n            try {\n                var _existingSessions_find, _messages_;\n                const storageKey = \"cellforge_conversations_\".concat((user === null || user === void 0 ? void 0 : user.id) || 'anonymous');\n                const existingSessions = JSON.parse(localStorage.getItem(storageKey) || '[]');\n                // 生成对话标题\n                const generateTitle = (msgs)=>{\n                    const firstUserMessage = msgs.find((m)=>m.type === \"user\");\n                    if (firstUserMessage) {\n                        const content = firstUserMessage.content.trim();\n                        return content.length > 30 ? content.substring(0, 30) + \"...\" : content;\n                    }\n                    return \"新对话\";\n                };\n                const sessionId = currentSessionId || \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n                const now = new Date();\n                const session = {\n                    id: sessionId,\n                    title: generateTitle(messages),\n                    messages,\n                    createdAt: currentSessionId ? ((_existingSessions_find = existingSessions.find((s)=>s.id === sessionId)) === null || _existingSessions_find === void 0 ? void 0 : _existingSessions_find.createdAt) || now : now,\n                    updatedAt: now,\n                    isStarred: false,\n                    isArchived: false,\n                    tags: [],\n                    messageCount: messages.length,\n                    lastMessage: ((_messages_ = messages[messages.length - 1]) === null || _messages_ === void 0 ? void 0 : _messages_.content.substring(0, 100)) || \"\"\n                };\n                // 更新或添加会话\n                const updatedSessions = currentSessionId ? existingSessions.map((s)=>s.id === sessionId ? session : s) : [\n                    session,\n                    ...existingSessions\n                ].slice(0, 50) // 最多保存50个对话\n                ;\n                localStorage.setItem(storageKey, JSON.stringify(updatedSessions));\n                setCurrentSessionId(sessionId);\n            } catch (error) {\n                console.error('自动保存对话失败:', error);\n            }\n        }\n    };\n    // 处理表单提交 - 使用增强的错误处理和分阶段加载\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim()) return;\n        const userMessage = inputValue.trim();\n        setInputValue(\"\");\n        setError(\"\");\n        // 创建用户消息\n        const newUserMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: userMessage,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newUserMessage\n            ]);\n        // 构建对话历史和上下文（在分阶段操作外部准备）\n        const conversationHistory = messages.map((msg)=>({\n                role: msg.type === \"user\" ? \"user\" : \"assistant\",\n                content: msg.content\n            }));\n        const enhancedContext = {\n            user_profile: {\n                id: user === null || user === void 0 ? void 0 : user.id,\n                organization: (user === null || user === void 0 ? void 0 : user.organization) || \"\",\n                role: (user === null || user === void 0 ? void 0 : user.role) || \"\",\n                expertise: (user === null || user === void 0 ? void 0 : user.expertise_areas) || \"\",\n                research_interests: (user === null || user === void 0 ? void 0 : user.research_interests) || \"\"\n            },\n            requirements: requirements,\n            conversation_history: conversationHistory,\n            enable_literature_search: enableLiteratureSearch\n        };\n        try {\n            // 使用分阶段加载处理\n            let aiApiResponse;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        // 模拟分析阶段\n                        await new Promise((resolve)=>setTimeout(resolve, 800));\n                        return {\n                            step: \"analyzed\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.SEARCHING_LITERATURE,\n                    operation: async ()=>{\n                        // 模拟文献搜索\n                        await new Promise((resolve)=>setTimeout(resolve, 1200));\n                        return {\n                            step: \"literature_searched\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        // 实际调用AI API\n                        aiApiResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.conversationApi.sendMessage({\n                            message: userMessage,\n                            conversation_id: conversationId || undefined,\n                            conversation_type: \"enhanced\",\n                            history: conversationHistory,\n                            context: enhancedContext\n                        });\n                        return aiApiResponse;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.FORMATTING,\n                    operation: async ()=>{\n                        // 模拟格式化处理\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        return {\n                            step: \"formatted\"\n                        };\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"AI服务暂时不可用，请稍后重试\",\n                showToast: false,\n                onRetry: (attempt, error)=>{\n                    var _error_message, _error_message1;\n                    // 只在网络错误时才显示重试信息\n                    if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('fetch')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('网络'))) {\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"检测到网络问题，正在重试...\");\n                    }\n                }\n            });\n            // aiApiResponse已经在GENERATING_RESPONSE阶段中设置\n            // 更新用户消息状态为已发送\n            setMessages((prev)=>prev.map((msg)=>msg.id === newUserMessage.id ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n            // 创建AI回复消息\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: aiApiResponse.message,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: aiApiResponse.confidence,\n                sources: aiApiResponse.sources,\n                suggestions: aiApiResponse.suggestions\n            };\n            setMessages((prev)=>{\n                const newMessages = [\n                    ...prev,\n                    aiResponse\n                ];\n                // 自动保存对话\n                setTimeout(()=>autoSaveConversation(newMessages), 1000);\n                return newMessages;\n            });\n            finishLoading();\n        } catch (err) {\n            console.error(\"发送消息失败:\", err);\n            // 更新用户消息状态为错误\n            setMessages((prev)=>prev.map((msg)=>msg.id === newUserMessage.id ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            // 错误已经由errorHandler处理，这里只需要更新UI状态\n            setError(\"发送消息失败，请稍后重试\");\n            cancelLoading();\n        }\n    };\n    // 处理需求变更\n    const handleRequirementsChange = (newRequirements)=>{\n        setRequirements(newRequirements);\n    };\n    // 处理需求提交 - 使用新的方案生成API\n    const handleRequirementsSubmit = async (requirements)=>{\n        // 生成需求总结消息\n        const requirementSummary = generateRequirementSummary(requirements);\n        // 添加用户消息到对话中\n        const userMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: requirementSummary,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        try {\n            var _solutionResponse_ai_analysis_features, _solutionResponse_solution_id, _solutionResponse_ai_analysis_features1;\n            // 使用分阶段加载处理需求提交，但现在专注于方案生成\n            let solutionResponse;\n            let literatureResults = null;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        // 验证需求完整性\n                        const validation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.solutionApi.validateRequirements(requirements);\n                        return validation;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.SEARCHING_LITERATURE,\n                    operation: async ()=>{\n                        // 如果启用了文献搜索，先执行文献搜索获得可见的结果\n                        if (enableLiteratureSearch) {\n                            try {\n                                // 调用智能文献搜索API获得用户可见的搜索过程\n                                const literatureResult = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.literatureApi.enhancedLiteratureSearch({\n                                    query: \"单细胞测序 \".concat(requirements.researchGoal, \" \").concat(requirements.sampleType),\n                                    requirements: requirements,\n                                    enable_literature_search: true\n                                });\n                                // 保存文献搜索结果以便后续使用\n                                literatureResults = literatureResult;\n                                return {\n                                    step: \"literature_evidence_collected\",\n                                    literature_results: literatureResult,\n                                    search_visible: true\n                                };\n                            } catch (error) {\n                                console.warn(\"文献搜索失败，继续方案生成:\", error);\n                                // 搜索失败也继续流程，在方案生成时会有内部文献支撑\n                                return {\n                                    step: \"literature_fallback_mode\"\n                                };\n                            }\n                        } else {\n                            // 文献搜索在后端内部进行（用户不可见）\n                            await new Promise((resolve)=>setTimeout(resolve, 800));\n                            return {\n                                step: \"literature_evidence_collected\",\n                                search_visible: false\n                            };\n                        }\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        // 优先调用综合方案框架API（按设计文档要求）\n                        try {\n                            console.log('🚀 开始调用综合方案框架API...');\n                            console.log('请求数据:', {\n                                requirements: requirements,\n                                user_message: requirementSummary,\n                                framework_template: 'standard',\n                                enable_literature_search: enableLiteratureSearch\n                            });\n                            const frameworkResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.solutionApi.generateComprehensiveFramework({\n                                requirements: requirements,\n                                user_message: requirementSummary,\n                                framework_template: 'standard',\n                                enable_literature_search: enableLiteratureSearch\n                            });\n                            console.log('✅ 综合方案框架API响应:', frameworkResponse);\n                            if (frameworkResponse.success) {\n                                var _frameworkResponse_data_solution_overview, _frameworkResponse_data_implementation_plan;\n                                // 将综合方案框架结果转换为前端需要的格式\n                                solutionResponse = {\n                                    solution_id: frameworkResponse.framework_id,\n                                    generated_at: frameworkResponse.generation_time,\n                                    client_requirements: requirements,\n                                    comprehensive_framework: frameworkResponse.data,\n                                    recommended_solution: {\n                                        platform: \"CellForge AI 综合方案框架\",\n                                        reasoning: \"基于设计文档的完整方案框架，包含方案概览、研究意图分析、关键要素、文献推荐、平台对比和实施规划\",\n                                        specifications: frameworkResponse.data.solution_overview\n                                    },\n                                    cost_analysis: ((_frameworkResponse_data_solution_overview = frameworkResponse.data.solution_overview) === null || _frameworkResponse_data_solution_overview === void 0 ? void 0 : _frameworkResponse_data_solution_overview.cost_analysis) || {},\n                                    risk_assessment: frameworkResponse.data.risk_assessment || {},\n                                    timeline: ((_frameworkResponse_data_implementation_plan = frameworkResponse.data.implementation_plan) === null || _frameworkResponse_data_implementation_plan === void 0 ? void 0 : _frameworkResponse_data_implementation_plan.timeline) || {},\n                                    deliverables: [\n                                        \"方案概览卡片\",\n                                        \"研究意图精准搜索链接\",\n                                        \"关键要素分析\",\n                                        \"智能文献推荐\",\n                                        \"平台对比分析\",\n                                        \"项目实施规划\"\n                                    ],\n                                    next_steps: [\n                                        \"查看完整的方案框架分析\",\n                                        \"使用精准搜索链接获取文献\",\n                                        \"根据平台对比选择技术方案\"\n                                    ],\n                                    contact_info: {\n                                        email: \"<EMAIL>\",\n                                        phone: \"************\"\n                                    },\n                                    framework_features: {\n                                        comprehensive_framework: true,\n                                        research_intent_analysis: true,\n                                        precision_search_links: true,\n                                        literature_recommendations: true,\n                                        platform_comparison: true\n                                    }\n                                };\n                            } else {\n                                throw new Error('综合方案框架API调用失败');\n                            }\n                        } catch (frameworkError) {\n                            console.error('❌ 综合方案框架API失败:', frameworkError);\n                            console.error('错误详情:', {\n                                message: frameworkError.message,\n                                stack: frameworkError.stack,\n                                name: frameworkError.name\n                            });\n                            // 使用简化的降级方案\n                            solutionResponse = {\n                                solution_id: \"fallback_\".concat(Date.now()),\n                                generated_at: new Date().toISOString(),\n                                client_requirements: requirements,\n                                recommended_solution: {\n                                    platform: \"CellForge AI 基础方案\",\n                                    reasoning: \"由于技术原因，当前提供基础方案。我们的专家将为您进行人工分析，确保方案的准确性和个性化。\",\n                                    specifications: {}\n                                },\n                                cost_analysis: {},\n                                risk_assessment: {},\n                                timeline: {},\n                                deliverables: [\n                                    \"人工专家分析报告\",\n                                    \"个性化技术方案设计\",\n                                    \"详细成本效益分析\"\n                                ],\n                                next_steps: [\n                                    \"联系专家进行人工分析\",\n                                    \"获取个性化方案设计\",\n                                    \"确定最优技术路线\"\n                                ],\n                                contact_info: {\n                                    email: \"<EMAIL>\",\n                                    phone: \"************\"\n                                },\n                                fallback_notice: {\n                                    message: \"为确保方案质量，建议联系专家进行人工分析\",\n                                    expert_contact: true,\n                                    reason: \"技术服务暂时不可用\"\n                                }\n                            };\n                        }\n                        return solutionResponse;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.FORMATTING,\n                    operation: async ()=>{\n                        // 格式化方案内容，如果有文献结果则包含进去\n                        await new Promise((resolve)=>setTimeout(resolve, 400));\n                        return {\n                            step: \"solution_formatted\"\n                        };\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"方案生成失败，请稍后重试\",\n                showToast: false,\n                onRetry: (attempt, error)=>{\n                    var _error_message, _error_message1;\n                    if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('fetch')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('网络'))) {\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"网络问题，正在重试生成方案...\");\n                    }\n                }\n            });\n            // 更新用户消息状态\n            setMessages((prev)=>prev.map((msg)=>msg.id === userMessage.id ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n            // 生成方案展示的AI回复，包含文献搜索结果（如果有）\n            const solutionContent = formatSolutionResponse(solutionResponse, literatureResults);\n            // 更新sources以反映实际的数据来源\n            let sources = [\n                \"方案生成系统\",\n                \"技术知识库\",\n                \"成本分析引擎\"\n            ];\n            // 根据不同的方案类型使用不同的数据来源\n            if ((_solutionResponse_ai_analysis_features = solutionResponse.ai_analysis_features) === null || _solutionResponse_ai_analysis_features === void 0 ? void 0 : _solutionResponse_ai_analysis_features.intent_analyzed) {\n                sources = [\n                    \"AI意图分析引擎\",\n                    \"用户行为分析\",\n                    \"个性化算法\",\n                    \"专业知识库\"\n                ];\n                if (solutionResponse.ai_analysis_features.analysis_sources) {\n                    sources = sources.concat(solutionResponse.ai_analysis_features.analysis_sources);\n                }\n            } else if (((_solutionResponse_solution_id = solutionResponse.solution_id) === null || _solutionResponse_solution_id === void 0 ? void 0 : _solutionResponse_solution_id.includes('intelligent')) || solutionResponse.intelligent_features) {\n                sources = [\n                    \"AI智能推荐引擎\",\n                    \"专业知识库\",\n                    \"热点文献发现\",\n                    \"技术规格数据库\",\n                    \"风险评估算法\"\n                ];\n            } else if (solutionResponse.fallback_notice) {\n                sources = [\n                    \"基础知识库\",\n                    \"专家经验库\",\n                    \"标准方案模板\"\n                ];\n            }\n            if (literatureResults && enableLiteratureSearch) {\n                sources.push(\"文献数据库\", \"智能搜索引擎\");\n            }\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: solutionContent,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: ((_solutionResponse_ai_analysis_features1 = solutionResponse.ai_analysis_features) === null || _solutionResponse_ai_analysis_features1 === void 0 ? void 0 : _solutionResponse_ai_analysis_features1.confidence_score) || (literatureResults ? 0.98 : solutionResponse.fallback_notice ? 0.70 : 0.95),\n                sources: sources,\n                suggestions: [\n                    \"查看详细的成本分析\",\n                    \"了解项目时间规划\",\n                    \"获取技术平台对比\",\n                    \"联系专家进行咨询\"\n                ]\n            };\n            setMessages((prev)=>{\n                const newMessages = [\n                    ...prev,\n                    aiResponse\n                ];\n                // 自动保存对话\n                setTimeout(()=>autoSaveConversation(newMessages), 1000);\n                return newMessages;\n            });\n            finishLoading();\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已基于您的需求生成专业方案！\");\n        } catch (err) {\n            console.error(\"生成建议失败:\", err);\n            // 更新用户消息状态为错误\n            setMessages((prev)=>prev.map((msg)=>msg.id === userMessage.id ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            cancelLoading();\n        // 错误已经由errorHandler处理\n        }\n    };\n    // 生成需求总结 - 更新以匹配新的字段结构\n    const generateRequirementSummary = (reqs)=>{\n        const sections = [];\n        sections.push(\"📋 **项目需求总结**\\n\");\n        // 基础分类信息\n        if (reqs.speciesType) sections.push(\"\\uD83E\\uDDEC **物种类型**: \".concat(reqs.speciesType));\n        if (reqs.experimentType) sections.push(\"\\uD83D\\uDD2C **实验类型**: \".concat(reqs.experimentType));\n        if (reqs.researchGoal) sections.push(\"\\uD83C\\uDFAF **研究目标**: \".concat(reqs.researchGoal));\n        // 样本信息\n        if (reqs.sampleType || reqs.sampleCount || reqs.sampleCondition || reqs.cellCount) {\n            sections.push(\"\\n🧪 **样本信息**\");\n            if (reqs.sampleType) sections.push(\"• 样本类型: \".concat(reqs.sampleType));\n            if (reqs.sampleCount) sections.push(\"• 样本数目: \".concat(reqs.sampleCount));\n            if (reqs.sampleCondition) sections.push(\"• 样本状态: \".concat(reqs.sampleCondition));\n            if (reqs.sampleProcessing) sections.push(\"• 处理方式: \".concat(reqs.sampleProcessing));\n            if (reqs.cellCount) sections.push(\"• 细胞数量: \".concat(reqs.cellCount));\n            if (reqs.cellViability) sections.push(\"• 细胞活力: \".concat(reqs.cellViability));\n        }\n        // 项目规划\n        if (reqs.budget || reqs.timeline || reqs.urgencyLevel) {\n            sections.push(\"\\n📅 **项目规划**\");\n            if (reqs.budget) sections.push(\"• 预算范围: \".concat(reqs.budget));\n            if (reqs.timeline) sections.push(\"• 项目周期: \".concat(reqs.timeline));\n            if (reqs.urgencyLevel) sections.push(\"• 紧急程度: \".concat(reqs.urgencyLevel));\n        }\n        // 技术细节\n        if (reqs.sequencingDepth || reqs.analysisType || reqs.dataAnalysisNeeds || reqs.needsCellSorting || reqs.specialRequirements) {\n            sections.push(\"\\n⚙️ **技术细节**\");\n            if (reqs.sequencingDepth) sections.push(\"• 测序深度: \".concat(reqs.sequencingDepth));\n            if (reqs.analysisType) sections.push(\"• 分析类型: \".concat(reqs.analysisType));\n            if (reqs.dataAnalysisNeeds) sections.push(\"• 数据分析需求: \".concat(reqs.dataAnalysisNeeds));\n            if (reqs.needsCellSorting) sections.push(\"• 细胞分选: \".concat(reqs.needsCellSorting));\n            if (reqs.specialRequirements) sections.push(\"• 特殊要求: \".concat(reqs.specialRequirements));\n        }\n        sections.push(\"\\n请为我生成详细的技术方案建议。\");\n        return sections.join(\"\\n\");\n    };\n    // 格式化方案响应，包含文献搜索结果和个性化信息\n    const formatSolutionResponse = (solutionResponse, literatureResults)=>{\n        var _literatureResults_literature_results;\n        if (!solutionResponse) return \"抱歉，方案生成失败。\";\n        const { solution_id, client_requirements, recommended_solution, cost_analysis, risk_assessment, timeline, deliverables, next_steps, contact_info, intelligent_features, intent_analysis, research_domain, personalization_level, ai_analysis_features, fallback_notice } = solutionResponse;\n        const sections = [];\n        // 检测是否为个性化推荐或AI分析结果\n        const isPersonalized = personalization_level === \"high\" || intent_analysis;\n        const isIntelligentRecommendation = (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('intelligent')) || intelligent_features;\n        const isAiAnalyzed = (ai_analysis_features === null || ai_analysis_features === void 0 ? void 0 : ai_analysis_features.intent_analyzed) || (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('ai_analyzed'));\n        const isFallback = fallback_notice || (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('fallback'));\n        // 根据个性化程度和研究领域定制标题\n        if (isFallback) {\n            sections.push(\"⚠️ **CellForge AI 基础方案**\");\n            sections.push(\"⏰ 系统正在升级，专家人工分析中...\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加回退通知\n            if (fallback_notice) {\n                sections.push(\"📢 **重要提示**\");\n                sections.push(\"• \".concat(fallback_notice.message));\n                if (fallback_notice.expert_contact) {\n                    sections.push(\"• 专家团队将在2小时内与您联系\");\n                }\n                sections.push(\"• 原因: \".concat(fallback_notice.reason));\n                sections.push(\"\");\n            }\n        } else if (isAiAnalyzed && ai_analysis_features) {\n            var _ai_analysis_features_analysis_sources;\n            sections.push(\"🧠 **CellForge AI 智能意图分析方案**\");\n            sections.push(\"✨ 基于深度用户意图分析的个性化推荐\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加AI分析摘要\n            sections.push(\"🔍 **AI意图分析结果**\");\n            sections.push(\"• 分析置信度：\".concat(Math.round((ai_analysis_features.confidence_score || 0.85) * 100), \"%\"));\n            if (((_ai_analysis_features_analysis_sources = ai_analysis_features.analysis_sources) === null || _ai_analysis_features_analysis_sources === void 0 ? void 0 : _ai_analysis_features_analysis_sources.length) > 0) {\n                sections.push(\"• 分析来源：\".concat(ai_analysis_features.analysis_sources.join(\", \")));\n            }\n            sections.push(\"• 个性化程度：高度定制\");\n            sections.push(\"\");\n        } else if (isPersonalized && research_domain) {\n            sections.push(\"\\uD83C\\uDFAF **CellForge AI \".concat(research_domain, \"专业方案**\"));\n            sections.push(\"✨ 基于意图分析的个性化推荐\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加意图分析摘要\n            if (intent_analysis) {\n                sections.push(\"🧠 **用户意图分析**\");\n                sections.push(\"• 研究领域：\".concat(intent_analysis.research_domain || '通用研究'));\n                sections.push(\"• 预算类别：\".concat(intent_analysis.budget_category || '标准型'));\n                sections.push(\"• 紧急程度：\".concat(intent_analysis.urgency_level || '常规'));\n                sections.push(\"• 技术偏好：\".concat(intent_analysis.technical_preference || '10x Genomics'));\n                sections.push(\"• 置信度：\".concat(Math.round((intent_analysis.confidence_score || 0.85) * 100), \"%\"));\n                sections.push(\"\");\n            }\n        } else if (isIntelligentRecommendation) {\n            sections.push(\"🎯 **CellForge AI 智能推荐方案**\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n        } else {\n            sections.push(\"🎯 **CellForge AI 专业方案**\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n        }\n        // 推荐方案 - 支持新格式和旧格式\n        if (recommended_solution === null || recommended_solution === void 0 ? void 0 : recommended_solution.platform) {\n            sections.push(\"🔬 **推荐技术方案**\");\n            // 新格式（智能推荐）\n            if (typeof recommended_solution.platform === 'string') {\n                sections.push(\"**平台**: \".concat(recommended_solution.platform));\n                sections.push(\"**理由**: \".concat(recommended_solution.reasoning));\n                if (recommended_solution.specifications) {\n                    sections.push(\"**规格**: \".concat(JSON.stringify(recommended_solution.specifications).replace(/[{}\",]/g, ' ').replace(/:/g, ': ')));\n                }\n            } else {\n                sections.push(\"**平台**: \".concat(recommended_solution.platform.primary));\n                sections.push(\"**理由**: \".concat(recommended_solution.platform.reason));\n                sections.push(\"**规格**: \".concat(recommended_solution.platform.specifications));\n                sections.push(\"**预期细胞数**: \".concat(recommended_solution.platform.expected_cells));\n            }\n            sections.push(\"\");\n        }\n        // 成本分析 - 支持新格式和旧格式\n        if (cost_analysis) {\n            sections.push(\"💰 **成本分析**\");\n            // 新格式（智能推荐）\n            if (cost_analysis.total_cost) {\n                sections.push(\"**总费用**: \\xa5\".concat((cost_analysis.total_cost / 10000).toFixed(1), \"万\"));\n                sections.push(\"**费用明细**:\");\n                if (cost_analysis.sample_preparation) {\n                    sections.push(\"• 样本制备: \\xa5\".concat((cost_analysis.sample_preparation.cost / 10000).toFixed(1), \"万\"));\n                }\n                if (cost_analysis.sequencing) {\n                    sections.push(\"• 测序费用: \\xa5\".concat((cost_analysis.sequencing.cost / 10000).toFixed(1), \"万\"));\n                }\n                if (cost_analysis.data_analysis) {\n                    sections.push(\"• 数据分析: \\xa5\".concat((cost_analysis.data_analysis.cost / 10000).toFixed(1), \"万\"));\n                }\n                if (cost_analysis.breakdown) {\n                    sections.push(\"• \".concat(cost_analysis.breakdown));\n                }\n            } else if (cost_analysis.total_cost_range) {\n                sections.push(\"**总费用**: \".concat(cost_analysis.total_cost_range));\n                if (cost_analysis.cost_breakdown) {\n                    sections.push(\"**费用明细**:\");\n                    Object.entries(cost_analysis.cost_breakdown).forEach((param)=>{\n                        let [key, value] = param;\n                        sections.push(\"• \".concat(key, \": \").concat(value));\n                    });\n                }\n            }\n            sections.push(\"\");\n        }\n        // 项目时间规划 - 支持新格式和旧格式\n        if (timeline) {\n            sections.push(\"⏰ **项目时间规划**\");\n            // 新格式（智能推荐）\n            if (timeline.total) {\n                sections.push(\"**总周期**: \".concat(timeline.total));\n                sections.push(\"**阶段安排**:\");\n                if (timeline.sample_prep) sections.push(\"• 样本准备: \".concat(timeline.sample_prep));\n                if (timeline.sequencing) sections.push(\"• 测序分析: \".concat(timeline.sequencing));\n                if (timeline.analysis) sections.push(\"• 数据分析: \".concat(timeline.analysis));\n            } else if (timeline.total_duration) {\n                sections.push(\"**总周期**: \".concat(timeline.total_duration));\n                if (timeline.phases) {\n                    sections.push(\"**阶段安排**:\");\n                    Object.entries(timeline.phases).forEach((param)=>{\n                        let [phase, desc] = param;\n                        sections.push(\"• \".concat(phase, \": \").concat(desc));\n                    });\n                }\n            }\n            sections.push(\"\");\n        }\n        // 风险评估 - 支持新格式和旧格式\n        if (risk_assessment) {\n            var _risk_assessment_technical_risks, _risk_assessment_identified_risks;\n            sections.push(\"⚠️ **风险评估**\");\n            if (risk_assessment.success_probability) {\n                sections.push(\"**成功概率**: \".concat(risk_assessment.success_probability));\n            }\n            if (risk_assessment.overall_risk_level) {\n                sections.push(\"**整体风险等级**: \".concat(risk_assessment.overall_risk_level));\n            }\n            // 新格式技术风险\n            if (((_risk_assessment_technical_risks = risk_assessment.technical_risks) === null || _risk_assessment_technical_risks === void 0 ? void 0 : _risk_assessment_technical_risks.length) > 0) {\n                sections.push(\"**主要风险**:\");\n                risk_assessment.technical_risks.forEach((risk)=>{\n                    if (typeof risk === 'object') {\n                        sections.push(\"• \".concat(risk.risk, \" (\").concat(risk.probability, \")\"));\n                        sections.push(\"  影响: \".concat(risk.impact));\n                        sections.push(\"  缓解: \".concat(risk.mitigation));\n                    } else {\n                        sections.push(\"• \".concat(risk));\n                    }\n                });\n            } else if (((_risk_assessment_identified_risks = risk_assessment.identified_risks) === null || _risk_assessment_identified_risks === void 0 ? void 0 : _risk_assessment_identified_risks.length) > 0) {\n                sections.push(\"**主要风险**:\");\n                risk_assessment.identified_risks.forEach((risk)=>{\n                    sections.push(\"• \".concat(risk));\n                });\n            }\n            sections.push(\"\");\n        }\n        // 交付物\n        if ((deliverables === null || deliverables === void 0 ? void 0 : deliverables.length) > 0) {\n            sections.push(\"📦 **交付物清单**\");\n            deliverables.forEach((item)=>{\n                sections.push(\"• \".concat(item));\n            });\n            sections.push(\"\");\n        }\n        // 下一步行动\n        if ((next_steps === null || next_steps === void 0 ? void 0 : next_steps.length) > 0) {\n            sections.push(\"🚀 **下一步行动**\");\n            next_steps.forEach((step, index)=>{\n                sections.push(\"\".concat(index + 1, \". \").concat(step));\n            });\n            sections.push(\"\");\n        }\n        // 添加智能推荐特性（如果是智能推荐）\n        if (intelligent_features) {\n            var _intelligent_features_hot_papers, _intelligent_features_literature_recommendations_combined_results, _intelligent_features_literature_recommendations, _intelligent_features_smart_insights, _intelligent_features_collaboration_opportunities;\n            // 智能文献推荐 - 修复数据结构访问\n            if (((_intelligent_features_hot_papers = intelligent_features.hot_papers) === null || _intelligent_features_hot_papers === void 0 ? void 0 : _intelligent_features_hot_papers.length) > 0) {\n                sections.push(\"🧬 **智能文献推荐报告**\");\n                sections.push(\"\");\n                sections.push(\"🔥 **热点文献发现**:\");\n                intelligent_features.hot_papers.forEach((paper, index)=>{\n                    const pubmedLink = paper.doi ? \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.doi), \"[DOI]\") : \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.title));\n                    const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(paper.title));\n                    sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title, \"**\"));\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal, \" \").concat(paper.impact_factor ? \"(IF: \".concat(paper.impact_factor, \")\") : ''));\n                    sections.push(\"\\uD83D\\uDCA1 \".concat(paper.reason));\n                    sections.push(\"\\uD83D\\uDD17 [PubMed](\".concat(pubmedLink, \") | [Google Scholar](\").concat(scholarLink, \")\"));\n                    sections.push(\"\");\n                });\n            }\n            // 智能关键词扩展 - 修复数据结构访问并添加搜索链接\n            if (intelligent_features.expanded_keywords) {\n                var _keywords_semantic_expansion, _keywords_trending_terms;\n                const keywords = intelligent_features.expanded_keywords;\n                if (((_keywords_semantic_expansion = keywords.semantic_expansion) === null || _keywords_semantic_expansion === void 0 ? void 0 : _keywords_semantic_expansion.length) > 0 || ((_keywords_trending_terms = keywords.trending_terms) === null || _keywords_trending_terms === void 0 ? void 0 : _keywords_trending_terms.length) > 0) {\n                    var _keywords_semantic_expansion1, _keywords_trending_terms1, _keywords_molecular_targets;\n                    sections.push(\"🔍 **智能关键词扩展与搜索链接**:\");\n                    sections.push(\"\");\n                    if (((_keywords_semantic_expansion1 = keywords.semantic_expansion) === null || _keywords_semantic_expansion1 === void 0 ? void 0 : _keywords_semantic_expansion1.length) > 0) {\n                        sections.push(\"**语义扩展关键词**:\");\n                        keywords.semantic_expansion.slice(0, 5).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \") | [Scholar](\").concat(scholarLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                    if (((_keywords_trending_terms1 = keywords.trending_terms) === null || _keywords_trending_terms1 === void 0 ? void 0 : _keywords_trending_terms1.length) > 0) {\n                        sections.push(\"**热点趋势关键词**:\");\n                        keywords.trending_terms.slice(0, 5).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \") | [Scholar](\").concat(scholarLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                    if (((_keywords_molecular_targets = keywords.molecular_targets) === null || _keywords_molecular_targets === void 0 ? void 0 : _keywords_molecular_targets.length) > 0) {\n                        sections.push(\"**分子靶点关键词**:\");\n                        keywords.molecular_targets.slice(0, 3).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                }\n            }\n            // 外部文献搜索结果\n            if (((_intelligent_features_literature_recommendations = intelligent_features.literature_recommendations) === null || _intelligent_features_literature_recommendations === void 0 ? void 0 : (_intelligent_features_literature_recommendations_combined_results = _intelligent_features_literature_recommendations.combined_results) === null || _intelligent_features_literature_recommendations_combined_results === void 0 ? void 0 : _intelligent_features_literature_recommendations_combined_results.length) > 0) {\n                sections.push(\"📚 **相关文献搜索结果**:\");\n                sections.push(\"\");\n                intelligent_features.literature_recommendations.combined_results.slice(0, 3).forEach((paper, index)=>{\n                    var _paper_authors;\n                    const pubmedLink = paper.pubmed_id ? \"https://pubmed.ncbi.nlm.nih.gov/\".concat(paper.pubmed_id, \"/\") : paper.doi ? \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.doi), \"[DOI]\") : \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.title));\n                    sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title, \"**\"));\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal, \" \").concat(paper.publication_year ? \"(\".concat(paper.publication_year, \")\") : ''));\n                    if (((_paper_authors = paper.authors) === null || _paper_authors === void 0 ? void 0 : _paper_authors.length) > 0) {\n                        sections.push(\"\\uD83D\\uDC65 \".concat(paper.authors.slice(0, 3).join(', ')).concat(paper.authors.length > 3 ? ' et al.' : ''));\n                    }\n                    if (paper.abstract) {\n                        sections.push(\"\\uD83D\\uDCDD \".concat(paper.abstract.substring(0, 200), \"...\"));\n                    }\n                    sections.push(\"\\uD83D\\uDD17 [查看原文](\".concat(pubmedLink, \") | 来源: \").concat(paper.source || 'Unknown'));\n                    sections.push(\"\");\n                });\n            }\n            // AI智能洞察\n            if (((_intelligent_features_smart_insights = intelligent_features.smart_insights) === null || _intelligent_features_smart_insights === void 0 ? void 0 : _intelligent_features_smart_insights.length) > 0) {\n                sections.push(\"💡 **AI智能洞察**\");\n                intelligent_features.smart_insights.forEach((insight)=>{\n                    sections.push(\"• \".concat(insight));\n                });\n                sections.push(\"\");\n            }\n            // 专家合作机会推荐\n            if (((_intelligent_features_collaboration_opportunities = intelligent_features.collaboration_opportunities) === null || _intelligent_features_collaboration_opportunities === void 0 ? void 0 : _intelligent_features_collaboration_opportunities.length) > 0) {\n                sections.push(\"🤝 **专家合作机会推荐**\");\n                intelligent_features.collaboration_opportunities.forEach((collab)=>{\n                    sections.push(\"• **\".concat(collab.institution, \"** - \").concat(collab.expert));\n                    sections.push(\"  \".concat(collab.collaboration_type));\n                });\n                sections.push(\"\");\n            }\n            // 智能文献检索建议\n            if (intelligent_features.search_suggestions) {\n                sections.push(\"🔍 **智能文献检索建议**\");\n                sections.push(\"\");\n                sections.push(\"📌 **推荐检索关键词**:\");\n                intelligent_features.search_suggestions.primaryKeywords.forEach((keyword, index)=>{\n                    sections.push(\"\".concat(index + 1, \". **\").concat(keyword, \"**\"));\n                    // 为每个主要关键词添加搜索链接\n                    const links = intelligent_features.search_suggestions.searchLinks[keyword];\n                    if (links && links.length > 0) {\n                        const linkTexts = links.map((link)=>\"[\".concat(link.emoji, \" \").concat(link.name, \"](\").concat(link.url, \")\")).join(' • ');\n                        sections.push(\"   \".concat(linkTexts));\n                    }\n                    sections.push(\"\");\n                });\n                if (intelligent_features.search_suggestions.secondaryKeywords.length > 0) {\n                    sections.push(\"🔖 **扩展检索关键词**:\");\n                    intelligent_features.search_suggestions.secondaryKeywords.slice(0, 4).forEach((keyword)=>{\n                        sections.push(\"• \".concat(keyword));\n                    });\n                    sections.push(\"\");\n                }\n                sections.push(\"💡 **检索建议**:\");\n                sections.push(\"• 建议先用主要关键词在PubMed中检索最新文献\");\n                sections.push(\"• 使用Google Scholar查找引用次数高的经典文献\");\n                sections.push(\"• Semantic Scholar可发现相关的综述文章\");\n                sections.push(\"• 组合关键词可获得更精准的搜索结果\");\n                sections.push(\"\");\n            }\n        }\n        // 添加文献搜索结果（如果启用了文献搜索）\n        if (literatureResults && ((_literatureResults_literature_results = literatureResults.literature_results) === null || _literatureResults_literature_results === void 0 ? void 0 : _literatureResults_literature_results.length) > 0) {\n            sections.push(\"\");\n            sections.push(\"📚 **相关文献支撑**\");\n            sections.push(\"\");\n            // 显示搜索到的文献（最多显示3篇）\n            const papersToShow = literatureResults.literature_results.slice(0, 3);\n            papersToShow.forEach((paper, index)=>{\n                sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title || '文献标题', \"**\"));\n                if (paper.authors && paper.authors.length > 0) {\n                    const authorList = paper.authors.slice(0, 3).join(\", \");\n                    const moreAuthors = paper.authors.length > 3 ? \" 等\" : \"\";\n                    sections.push(\"*\".concat(authorList).concat(moreAuthors, \"*\"));\n                }\n                if (paper.journal) {\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal).concat(paper.publication_year ? \" (\".concat(paper.publication_year, \")\") : ''));\n                }\n                if (paper.key_findings) {\n                    sections.push(\"\\uD83D\\uDCA1 **核心发现**: \".concat(paper.key_findings));\n                }\n                if (paper.methodology_summary) {\n                    sections.push(\"\\uD83D\\uDD2C **方法**: \".concat(paper.methodology_summary));\n                }\n                sections.push(\"\");\n            });\n            // 显示搜索统计信息\n            if (literatureResults.total_papers > 3) {\n                sections.push(\"\\uD83D\\uDCCA **搜索统计**: 共找到 \".concat(literatureResults.total_papers, \" 篇相关文献，已显示最相关的 \").concat(papersToShow.length, \" 篇\"));\n                sections.push(\"\");\n            }\n            // 显示数据来源\n            if (literatureResults.sources && literatureResults.sources.length > 0) {\n                sections.push(\"\\uD83D\\uDD0D **数据来源**: \".concat(literatureResults.sources.join(\", \")));\n                sections.push(\"\");\n            }\n        }\n        // 处理综合方案框架（按设计文档要求）\n        const { comprehensive_framework, framework_features } = solutionResponse;\n        if (comprehensive_framework && (framework_features === null || framework_features === void 0 ? void 0 : framework_features.comprehensive_framework)) {\n            sections.push(\"\");\n            sections.push(\"🎯 **CellForge AI 综合方案框架**\");\n            sections.push(\"\");\n            // 添加综合方案框架的JSON数据供前端组件解析\n            sections.push(\"```json\");\n            sections.push(JSON.stringify({\n                type: \"comprehensive_framework\",\n                data: comprehensive_framework\n            }, null, 2));\n            sections.push(\"```\");\n            sections.push(\"\");\n            sections.push(\"💡 **框架功能说明**:\");\n            sections.push(\"• **方案概览**: 研究类型、成本估算、平台推荐和风险等级\");\n            sections.push(\"• **研究意图分析**: 精准搜索链接和研究焦点展示\");\n            sections.push(\"• **关键要素分析**: 成功关键因素和风险评估\");\n            sections.push(\"• **文献推荐**: 核心文献和最新研究推荐\");\n            sections.push(\"• **平台对比**: 技术平台推荐排序和对比分析\");\n            sections.push(\"• **实施规划**: 项目时间轴和里程碑规划\");\n            sections.push(\"\");\n        }\n        // 处理综合解决方案（5个核心功能）- 作为降级方案\n        const { comprehensive_solution, enhanced_features } = solutionResponse;\n        if (comprehensive_solution && (enhanced_features === null || enhanced_features === void 0 ? void 0 : enhanced_features.five_core_functions)) {\n            sections.push(\"\");\n            sections.push(\"🎯 **CellForge AI 5大核心功能综合分析**\");\n            sections.push(\"\");\n            // 添加综合解决方案的JSON数据供前端组件解析\n            sections.push(\"```json\");\n            sections.push(JSON.stringify({\n                type: \"comprehensive_solution\",\n                data: comprehensive_solution\n            }, null, 2));\n            sections.push(\"```\");\n            sections.push(\"\");\n            sections.push(\"💡 **功能说明**:\");\n            sections.push(\"• **个性化方案**: 基于您的需求定制的专业技术方案\");\n            sections.push(\"• **文献推荐**: 领域相关的高质量学术文献\");\n            sections.push(\"• **搜索关键词**: 优化的学术检索关键词组合\");\n            sections.push(\"• **痛点分析**: 该领域常见技术挑战及解决方案\");\n            sections.push(\"• **风险评估**: 项目实施风险评估及缓解策略\");\n            sections.push(\"\");\n        }\n        sections.push(\"---\");\n        if (isFallback) {\n            sections.push(\"🧬 **CellForge AI** - 您的单细胞测序专业伙伴\");\n            sections.push(\"\");\n            sections.push(\"**服务状态**: 临时方案 • **专家支持**: 人工分析中 • **预计响应**: 2小时内\");\n        } else if (isAiAnalyzed) {\n            sections.push(\"🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴\");\n            sections.push(\"\");\n            const confidence = (ai_analysis_features === null || ai_analysis_features === void 0 ? void 0 : ai_analysis_features.confidence_score) ? Math.round(ai_analysis_features.confidence_score * 100) : 95;\n            sections.push(\"**AI分析置信度**: \".concat(confidence, \"% • **来源**: AI意图分析引擎, 专业知识库, 个性化算法\"));\n        } else if (isIntelligentRecommendation) {\n            sections.push(\"🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴\");\n            sections.push(\"\");\n            sections.push(\"**置信度**: 95% • **来源**: AI智能推荐引擎, 专业知识库, 热点文献发现\");\n        } else {\n            sections.push(\"🧬 **CellForge AI** - 您的单细胞测序专业伙伴\");\n        }\n        return sections.join(\"\\n\");\n    };\n    // 基于需求生成AI建议 - 使用增强的加载状态\n    const generateRequirementBasedSuggestion = async (reqs)=>{\n        const suggestionMessage = \"基于您填写的需求信息：\\n- 研究目标：\".concat(reqs.researchGoal, \"\\n- 样本类型：\").concat(reqs.sampleType, \"\\n- 细胞数量：\").concat(reqs.cellCount, \"\\n- 预算范围：\").concat(reqs.budget, \"\\n- 项目周期：\").concat(reqs.timeline, \"\\n\\n我来为您生成专业的技术方案建议。\");\n        try {\n            // 使用分阶段加载\n            let apiResponse;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        return {\n                            step: \"analyzed\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        apiResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.conversationApi.sendMessage({\n                            message: suggestionMessage,\n                            conversation_type: \"requirement_based\",\n                            context: {\n                                user_profile: {\n                                    id: user === null || user === void 0 ? void 0 : user.id,\n                                    organization: (user === null || user === void 0 ? void 0 : user.organization) || \"\",\n                                    role: (user === null || user === void 0 ? void 0 : user.role) || \"\"\n                                },\n                                requirements: reqs\n                            }\n                        });\n                        return apiResponse;\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"生成建议失败，请稍后重试\",\n                showToast: false\n            });\n            // apiResponse已经在GENERATING_RESPONSE阶段中设置\n            const aiSuggestion = {\n                id: Date.now().toString(),\n                type: \"ai\",\n                content: apiResponse.message,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: apiResponse.confidence,\n                sources: apiResponse.sources,\n                suggestions: apiResponse.suggestions\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiSuggestion\n                ]);\n            finishLoading();\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已基于您的需求生成专业建议！\");\n        } catch (err) {\n            console.error(\"生成建议失败:\", err);\n            cancelLoading();\n        // 错误已经由errorHandler处理\n        }\n    };\n    // 获取缺失的关键需求字段 - 更新以匹配新的必需字段\n    const getMissingRequirements = ()=>{\n        const requiredFields = [\n            {\n                key: \"speciesType\",\n                label: \"物种类型\",\n                question: \"您研究的是什么物种？\"\n            },\n            {\n                key: \"experimentType\",\n                label: \"实验类型\",\n                question: \"您需要什么类型的单细胞实验？\"\n            },\n            {\n                key: \"researchGoal\",\n                label: \"研究目标\",\n                question: \"您的研究目标是什么？\"\n            },\n            {\n                key: \"sampleType\",\n                label: \"样本类型\",\n                question: \"您使用什么类型的样本？\"\n            },\n            {\n                key: \"budget\",\n                label: \"预算范围\",\n                question: \"您的项目预算大概是多少？\"\n            }\n        ];\n        if (!requirements) return requiredFields;\n        return requiredFields.filter((field)=>!requirements[field.key]);\n    };\n    // 获取智能建议（合并AI建议和快速回复）\n    const getSmartSuggestions = ()=>{\n        // 优先显示AI回复中的建议\n        const lastMessage = messages[messages.length - 1];\n        if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.type) === \"ai\" && lastMessage.suggestions && lastMessage.suggestions.length > 0) {\n            return {\n                type: \"ai_suggestions\",\n                title: \"AI智能建议\",\n                icon: \"✨\",\n                items: lastMessage.suggestions.slice(0, 4).map((suggestion)=>({\n                        text: suggestion,\n                        icon: \"💡\"\n                    }))\n            };\n        }\n        // 检查是否有缺失的关键需求信息\n        const missingReqs = getMissingRequirements();\n        if (missingReqs.length > 0 && (!requirements || requirements.completeness < 60)) {\n            return {\n                type: \"requirement_framework\",\n                title: \"完善需求信息\",\n                icon: \"📋\",\n                subtitle: \"还需要填写 \".concat(missingReqs.length, \" 项关键信息，建议先完善后再提交\"),\n                items: missingReqs.slice(0, 3).map((req)=>({\n                        text: req.question,\n                        icon: \"❓\",\n                        action: \"fill_requirement\",\n                        field: req.key\n                    }))\n            };\n        }\n        // 否则显示基于需求完成度的快速开始建议\n        if (!requirements || requirements.completeness < 20) {\n            return {\n                type: \"quick_start\",\n                title: \"快速开始\",\n                icon: \"💬\",\n                items: [\n                    {\n                        text: \"我需要单细胞RNA测序方案\",\n                        icon: \"🧬\"\n                    },\n                    {\n                        text: \"请推荐技术平台\",\n                        icon: \"⚡\"\n                    },\n                    {\n                        text: \"分析项目成本\",\n                        icon: \"💰\"\n                    },\n                    {\n                        text: \"查看成功案例\",\n                        icon: \"📊\"\n                    }\n                ]\n            };\n        } else if (requirements.completeness < 80) {\n            return {\n                type: \"continue_conversation\",\n                title: \"继续对话\",\n                icon: \"🔄\",\n                items: [\n                    {\n                        text: \"继续完善需求信息\",\n                        icon: \"📝\"\n                    },\n                    {\n                        text: \"基于当前信息给建议\",\n                        icon: \"💡\"\n                    },\n                    {\n                        text: \"了解技术细节\",\n                        icon: \"🔬\"\n                    },\n                    {\n                        text: \"预估项目周期\",\n                        icon: \"⏱️\"\n                    }\n                ]\n            };\n        } else {\n            return {\n                type: \"advanced_actions\",\n                title: \"高级功能\",\n                icon: \"🚀\",\n                items: [\n                    {\n                        text: \"生成完整技术方案\",\n                        icon: \"📋\"\n                    },\n                    {\n                        text: \"优化成本配置\",\n                        icon: \"💰\"\n                    },\n                    {\n                        text: \"风险评估分析\",\n                        icon: \"⚠️\"\n                    },\n                    {\n                        text: \"联系技术专家\",\n                        icon: \"👨‍🔬\"\n                    }\n                ]\n            };\n        }\n    };\n    // 处理建议点击\n    const handleSuggestionClick = (item)=>{\n        if (item.action === \"fill_requirement\") {\n            // 如果是需求填写建议，显示右侧面板并聚焦到对应字段\n            setRightPanelCollapsed(false);\n            // 可以添加滚动到对应字段的逻辑\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"请在右侧面板填写：\".concat(item.text));\n        } else {\n            // 普通建议直接设置到输入框\n            setInputValue(item.text);\n        }\n    };\n    // 加载历史对话\n    const handleLoadConversation = (session)=>{\n        setMessages(session.messages);\n        setCurrentSessionId(session.id);\n        setShowHistory(false);\n        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已加载对话: \".concat(session.title));\n    };\n    // 保存当前对话\n    const handleSaveCurrentConversation = ()=>{\n        // 这个函数会在ConversationHistory组件中处理\n        setShowHistory(false);\n    };\n    // 开始新对话\n    const handleNewConversation = ()=>{\n        setMessages([]);\n        setCurrentSessionId(null);\n        setRequirements(null);\n        setError(\"\");\n        setShowHistory(false);\n        setRightPanelCollapsed(false) // 显示需求收集助手\n        ;\n        setResetTrigger((prev)=>prev + 1) // 触发需求收集助手重置\n        ;\n        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已开始新对话，请在右侧填写项目需求\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full transition-all duration-300 \".concat(rightPanelCollapsed ? 'flex-1' : 'flex-1'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-slate-200 px-4 py-3 flex items-center justify-between flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1371,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1370,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-semibold text-slate-900\",\n                                                        children: \"CellForge AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-slate-500\",\n                                                        children: \"单细胞测序智能顾问\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1375,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1373,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 13\n                                    }, this),\n                                    requirements && requirements.completeness > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-blue-100 text-blue-800\",\n                                        children: [\n                                            \"需求完成度 \",\n                                            requirements.completeness,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowHistory(!showHistory),\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1393,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"历史\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1387,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleNewConversation,\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"新对话\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1398,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setRightPanelCollapsed(!rightPanelCollapsed),\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: rightPanelCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1417,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"显示助手\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"隐藏助手\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1423,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1385,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1367,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                            variant: \"destructive\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1435,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1436,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1434,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1433,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto p-4 space-y-6 bg-white\",\n                            children: [\n                                messages.map((message)=>{\n                                    var _user_name;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                        children: [\n                                            message.type === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1449,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1448,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(message.type === \"user\" ? \"max-w-lg ml-12\" : \"max-w-4xl mr-8\" // Give AI messages more space\n                                                ),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(message.type === \"user\" ? \"px-4 py-3 rounded-2xl shadow-sm\" : \"px-6 py-4 rounded-2xl shadow-sm\" // More padding for AI messages\n                                                        , \" \").concat(message.type === \"user\" ? message.status === \"error\" ? \"bg-red-500 text-white\" : \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white\" : \"bg-white border border-slate-200 text-slate-900\" // White background for AI\n                                                        ),\n                                                        children: [\n                                                            message.type === \"ai\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_formatted_message__WEBPACK_IMPORTED_MODULE_12__.FormattedMessage, {\n                                                                content: message.content,\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1472,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1474,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.type === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 pt-2 border-t border-slate-200 space-y-1\",\n                                                                children: [\n                                                                    message.confidence !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1482,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-slate-600\",\n                                                                                children: [\n                                                                                    \"置信度: \",\n                                                                                    Math.round(message.confidence * 100),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1483,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                        lineNumber: 1481,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1490,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-slate-600\",\n                                                                                children: [\n                                                                                    \"来源: \",\n                                                                                    message.sources.slice(0, 2).join(\", \"),\n                                                                                    message.sources.length > 2 && \"...\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1491,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                        lineNumber: 1489,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1479,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-2 flex items-center \".concat(message.type === \"user\" ? \"justify-end text-slate-500\" : \"justify-start text-slate-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: message.timestamp.toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1504,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: [\n                                                                    message.status === \"sending\" && \"⏳\",\n                                                                    message.status === \"sent\" && \"✓\",\n                                                                    message.status === \"read\" && \"✓✓\",\n                                                                    message.status === \"error\" && \"❌\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1506,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1453,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0)) || \"U\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1518,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1517,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1446,\n                                        columnNumber: 15\n                                    }, this);\n                                }),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1529,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                            lineNumber: 1528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 max-w-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.EnhancedLoadingIndicator, {\n                                                isLoading: isLoading,\n                                                stage: currentStage,\n                                                progress: progress,\n                                                estimatedTime: 30,\n                                                onCancel: cancelLoading,\n                                                onRetry: ()=>{\n                                                    cancelLoading();\n                                                    handleSendMessage();\n                                                },\n                                                showRetryAfter: 60,\n                                                allowCancel: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1532,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                            lineNumber: 1531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1527,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1548,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1444,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1442,\n                        columnNumber: 9\n                    }, this),\n                    (()=>{\n                        const suggestions = getSmartSuggestions();\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-slate-200 p-4 flex-shrink-0 \".concat(suggestions.type === \"ai_suggestions\" ? \"bg-gradient-to-r from-blue-50 to-indigo-50\" : suggestions.type === \"requirement_framework\" ? \"bg-gradient-to-r from-amber-50 to-orange-50\" : \"bg-white\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            suggestions.type === \"ai_suggestions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1566,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: suggestions.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1568,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium \".concat(suggestions.type === \"ai_suggestions\" ? \"text-blue-800\" : suggestions.type === \"requirement_framework\" ? \"text-amber-800\" : \"text-slate-600\"),\n                                                children: suggestions.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1570,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1564,\n                                        columnNumber: 17\n                                    }, this),\n                                    suggestions.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-amber-700 mb-3 p-2 bg-amber-100 rounded-lg border border-amber-200\",\n                                        children: suggestions.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1583,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: suggestions.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleSuggestionClick(item),\n                                                className: \"text-xs \".concat(suggestions.type === \"ai_suggestions\" ? \"bg-white hover:bg-blue-50 border-blue-200 text-blue-700 hover:text-blue-800\" : suggestions.type === \"requirement_framework\" ? \"bg-white hover:bg-amber-50 border-amber-200 text-amber-700 hover:text-amber-800\" : \"bg-slate-50 hover:bg-slate-100 border-slate-200\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1603,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.text\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1590,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1588,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1563,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1556,\n                            columnNumber: 13\n                        }, this);\n                    })(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-200 p-4 bg-white flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 bg-slate-50 rounded-xl p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"text-slate-500 hover:text-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1617,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"text-slate-500 hover:text-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1620,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1619,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: inputValue,\n                                    onChange: (e)=>setInputValue(e.target.value),\n                                    placeholder: \"输入您的问题，我来为您提供专业建议...\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && !e.shiftKey && handleSendMessage(),\n                                    className: \"flex-1 border-0 bg-white shadow-sm focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1622,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !inputValue.trim() || isLoading,\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1634,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1629,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1615,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1614,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1363,\n                columnNumber: 7\n            }, this),\n            (!rightPanelCollapsed || showHistory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-l border-slate-200 bg-white transition-all duration-300 flex flex-col h-full \".concat(isMobile ? 'absolute right-0 top-0 bottom-0 w-80 shadow-xl z-10' : 'w-80 flex-shrink-0'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-200 p-4 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: showHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1651,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-slate-900\",\n                                                    children: \"对话历史\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1652,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1656,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-slate-900\",\n                                                    children: \"需求收集助手\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1657,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            !showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowHistory(true),\n                                                className: \"text-slate-500 hover:text-slate-700 h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1671,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1665,\n                                                columnNumber: 19\n                                            }, this),\n                                            showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowHistory(false),\n                                                className: \"text-slate-500 hover:text-slate-700 h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1681,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    setRightPanelCollapsed(true);\n                                                    setShowHistory(false);\n                                                },\n                                                className: \"text-slate-500 h-6 w-6 p-0\",\n                                                title: \"隐藏面板\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1695,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1685,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1662,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1647,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-slate-600 mt-1\",\n                                children: showHistory ? \"查看和管理您的对话历史\" : \"快速填写项目需求，获得更精准的AI建议\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1699,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1646,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 relative\",\n                        children: showHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_conversation_history__WEBPACK_IMPORTED_MODULE_13__.ConversationHistory, {\n                                onLoadConversation: handleLoadConversation,\n                                currentMessages: messages,\n                                onSaveCurrentConversation: handleSaveCurrentConversation\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1711,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1710,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_literature_search_toggle__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    enabled: enableLiteratureSearch,\n                                    onToggle: setEnableLiteratureSearch,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1720,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_progressive_requirement_collector__WEBPACK_IMPORTED_MODULE_11__.ProgressiveRequirementCollector, {\n                                    onRequirementsChange: handleRequirementsChange,\n                                    onSubmitRequirements: handleRequirementsSubmit,\n                                    isCompact: true,\n                                    resetTrigger: resetTrigger\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1727,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1718,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1708,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1642,\n                columnNumber: 9\n            }, this),\n            isMobile && !rightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-5\",\n                onClick: ()=>setRightPanelCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1741,\n                columnNumber: 9\n            }, this),\n            isMobile && rightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"fixed bottom-4 right-4 rounded-full w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg z-10\",\n                onClick: ()=>setRightPanelCollapsed(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                    lineNumber: 1753,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1749,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n        lineNumber: 1361,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationInterface, \"YqxGvOJHMwSGRzXwQIpKNTlg44s=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.useStageLoading\n    ];\n});\n_c = ConversationInterface;\nvar _c;\n$RefreshReg$(_c, \"ConversationInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/conversation-interface.tsx\n"));

/***/ })

});