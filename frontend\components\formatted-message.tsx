import React, { useEffect } from 'react'
import { Copy, ExternalLink, CheckCircle, AlertTriangle, Info, Lightbulb } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { InteractiveLiteratureCitation } from './interactive-literature-citation'
import { ComprehensiveSolutionDisplay } from './comprehensive-solution-display'
import { ComprehensiveSolutionFramework } from './comprehensive-solution-framework'

interface FormattedMessageProps {
  content: string
  className?: string
}

export function FormattedMessage({ content, className = '' }: FormattedMessageProps) {
  // 渲染Mermaid图表的简化版本
  useEffect(() => {
    const renderMermaidDiagrams = () => {
      const diagrams = document.querySelectorAll('.mermaid-diagram[data-mermaid]:not(.mermaid-processed)')

      diagrams.forEach((diagram) => {
        const element = diagram as HTMLElement
        const mermaidCode = element.getAttribute('data-mermaid')

        if (!mermaidCode) return

        // 标记为已处理
        element.classList.add('mermaid-processed')

        // 创建一个更好的展示界面
        element.innerHTML = `
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
            <div class="text-center mb-4">
              <div class="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                <svg class="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
                Mermaid 图表
              </div>
            </div>
            <div class="bg-white rounded border border-slate-200 p-4">
              <div class="text-center text-slate-600 mb-3">
                <div class="text-lg mb-2">📊</div>
                <div class="text-sm font-medium">图表内容预览</div>
              </div>
              <pre class="text-xs text-slate-700 bg-slate-50 p-3 rounded border overflow-x-auto whitespace-pre-wrap">${mermaidCode}</pre>
              <div class="mt-3 text-center">
                <div class="text-xs text-slate-500">
                  💡 提示：此图表包含 ${mermaidCode.split('\\n').length} 行定义
                </div>
              </div>
            </div>
          </div>
        `
      })
    }

    // 延迟处理，确保DOM已更新
    const timer = setTimeout(renderMermaidDiagrams, 100)
    return () => clearTimeout(timer)
  }, [content])

  // 处理内联格式（粗体、斜体、代码、链接等）
  const formatInlineText = (text: string): React.ReactNode[] => {
    // 如果没有特殊格式，直接返回
    if (!text.includes('**') && !text.includes('*') && !text.includes('`') && !text.includes('~~') && !text.includes('[')) {
      return [text]
    }

    const parts: React.ReactNode[] = []
    let key = 0

    // 改进的正则表达式，更精确地处理markdown格式
    // 优先匹配较长的格式（如 *** 和 **），避免误匹配，并支持链接
    const formatRegex = /(\[([^\]]+?)\]\(([^)]+?)(?:\s+"([^"]*)")?\)|\*\*\*([^*\n]+?)\*\*\*|\*\*([^*\n]+?)\*\*|~~([^~\n]+?)~~|(?<!\*)\*([^*\n]+?)\*(?!\*)|`([^`\n]+?)`)/g
    let lastIndex = 0
    let match

    // 重置正则表达式的lastIndex
    formatRegex.lastIndex = 0

    while ((match = formatRegex.exec(text)) !== null) {
      // 添加格式前的普通文本
      if (match.index > lastIndex) {
        const plainText = text.slice(lastIndex, match.index)
        if (plainText) {
          parts.push(plainText)
        }
      }

      // 根据匹配的格式添加相应的元素
      if (match[2] && match[3]) {
        // 链接 [text](url) 或 [text](url "title")
        const linkText = match[2]
        const linkUrl = match[3]
        const linkTitle = match[4] || ''
        
        parts.push(
          <a 
            key={key++} 
            href={linkUrl}
            title={linkTitle}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline hover:no-underline transition-colors duration-200 font-medium inline-flex items-center gap-1"
          >
            {linkText}
            <ExternalLink className="h-3 w-3 opacity-60" />
          </a>
        )
      } else if (match[5]) {
        // 粗体斜体 ***text***
        parts.push(
          <strong key={key++} className="font-bold italic text-slate-900">
            {match[5]}
          </strong>
        )
      } else if (match[6]) {
        // 粗体 **text**
        parts.push(
          <strong key={key++} className="font-semibold text-slate-900">
            {match[6]}
          </strong>
        )
      } else if (match[7]) {
        // 删除线 ~~text~~
        parts.push(
          <span key={key++} className="line-through text-slate-600">
            {match[7]}
          </span>
        )
      } else if (match[8]) {
        // 斜体 *text* (使用负向前瞻和后瞻避免与**冲突)
        parts.push(
          <em key={key++} className="italic text-slate-800">
            {match[8]}
          </em>
        )
      } else if (match[9]) {
        // 代码 `text`
        parts.push(
          <code key={key++} className="bg-slate-100 text-slate-800 px-1 py-0.5 rounded text-sm font-mono">
            {match[9]}
          </code>
        )
      }

      lastIndex = match.index + match[0].length
    }

    // 添加剩余的普通文本
    if (lastIndex < text.length) {
      const remainingPlainText = text.slice(lastIndex)
      if (remainingPlainText) {
        parts.push(remainingPlainText)
      }
    }

    return parts.length > 0 ? parts : [text]
  }

  // 复制文本到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('已复制到剪贴板')
    }).catch(() => {
      toast.error('复制失败')
    })
  }

  const formatContent = (text: string) => {
    const lines = text.split('\n')
    const elements: React.ReactNode[] = []
    let key = 0
    let inCodeBlock = false
    let codeBlockContent: string[] = []
    let codeBlockLanguage = ''

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const trimmedLine = line.trim()

      // 处理代码块
      if (trimmedLine.startsWith('```')) {
        if (!inCodeBlock) {
          // 开始代码块
          inCodeBlock = true
          codeBlockLanguage = trimmedLine.slice(3).trim()
          codeBlockContent = []
        } else {
          // 结束代码块
          inCodeBlock = false
          const codeContent = codeBlockContent.join('\n')

          // 检查是否是综合解决方案或综合方案框架的JSON数据
          if (codeBlockLanguage === 'json') {
            try {
              const jsonData = JSON.parse(codeContent)
              if (jsonData.type === 'comprehensive_framework' && jsonData.data) {
                // 渲染综合方案框架组件（按设计文档要求）
                elements.push(
                  <div key={key++} className="my-6">
                    <ComprehensiveSolutionFramework
                      frameworkData={jsonData.data}
                      onSearchClick={(platform: string, url: string) => {
                        window.open(url, '_blank')
                      }}
                      onOptimize={(feedback: any) => {
                        console.log('优化反馈:', feedback)
                      }}
                    />
                  </div>
                )
              } else if (jsonData.type === 'comprehensive_solution' && jsonData.data) {
                // 渲染综合解决方案组件（降级方案）
                elements.push(
                  <div key={key++} className="my-6">
                    <ComprehensiveSolutionDisplay solutionData={jsonData.data} />
                  </div>
                )
              } else {
                // 普通JSON代码块
                elements.push(
                  <div key={key++} className="my-4 relative group">
                    <div className="bg-slate-900 rounded-lg overflow-hidden">
                      <div className="flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700">
                        <span className="text-xs text-slate-300 font-medium">JSON</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(codeContent)}
                          className="h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                      <pre className="p-4 text-sm text-slate-100 overflow-x-auto">
                        <code>{codeContent}</code>
                      </pre>
                    </div>
                  </div>
                )
              }
            } catch (jsonError) {
              // JSON解析失败，作为普通代码块处理
              elements.push(
                <div key={key++} className="my-4 relative group">
                  <div className="bg-slate-900 rounded-lg overflow-hidden">
                    <div className="flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700">
                      <span className="text-xs text-slate-300 font-medium">JSON</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(codeContent)}
                        className="h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                    <pre className="p-4 text-sm text-slate-100 overflow-x-auto">
                      <code>{codeContent}</code>
                    </pre>
                  </div>
                </div>
              )
            }
          }
          // 检查是否是Mermaid图表
          else if (codeBlockLanguage === 'mermaid' || codeContent.trim().match(/^(graph|pie|gantt|sequenceDiagram|classDiagram|stateDiagram|journey|gitgraph|flowchart)/)) {
            elements.push(
              <div key={key++} className="my-4">
                <div className="bg-white border border-slate-200 rounded-lg overflow-hidden">
                  <div className="flex items-center justify-between px-4 py-2 bg-slate-50 border-b border-slate-200">
                    <span className="text-sm text-slate-600 font-medium flex items-center gap-2">
                      <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                      </svg>
                      Mermaid 图表
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(codeContent)}
                      className="h-6 px-2 text-slate-400 hover:text-slate-600"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="p-4">
                    <div
                      className="mermaid-diagram bg-white rounded border border-slate-100 p-4 text-center min-h-[200px] flex flex-col items-center justify-center"
                      data-mermaid={codeContent}
                    >
                      <div className="text-slate-500 text-sm mb-2">🎨 正在渲染图表...</div>
                      <div className="text-xs text-slate-400 bg-slate-50 p-2 rounded text-left overflow-x-auto max-w-full">
                        <pre className="whitespace-pre-wrap">{codeContent}</pre>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          } else {
            // 普通代码块
            elements.push(
              <div key={key++} className="my-4 relative group">
                <div className="bg-slate-900 rounded-lg overflow-hidden">
                  <div className="flex items-center justify-between px-4 py-2 bg-slate-800 border-b border-slate-700">
                    <span className="text-xs text-slate-300 font-medium">
                      {codeBlockLanguage || 'Code'}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(codeContent)}
                      className="h-6 px-2 text-slate-400 hover:text-white hover:bg-slate-700"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <pre className="p-4 text-sm text-slate-100 overflow-x-auto">
                    <code>{codeContent}</code>
                  </pre>
                </div>
              </div>
            )
          }

          codeBlockContent = []
          codeBlockLanguage = ''
        }
        continue
      }

      // 如果在代码块内，收集内容
      if (inCodeBlock) {
        codeBlockContent.push(line)
        continue
      }

      // Skip empty lines but add spacing
      if (!trimmedLine) {
        elements.push(<div key={key++} className="h-2" />)
        continue
      }

      // Markdown标题支持 (### ## #)
      if (trimmedLine.match(/^#{1,6}\s+(.+)/)) {
        const match = trimmedLine.match(/^(#{1,6})\s+(.+)/)
        if (match) {
          const [, hashes, title] = match
          const level = hashes.length

          let className = ""
          let Component = "h1" as keyof React.JSX.IntrinsicElements

          switch (level) {
            case 1:
              Component = "h1"
              className = "text-2xl font-bold text-slate-900 mt-6 mb-4 pb-2 border-b border-slate-200"
              break
            case 2:
              Component = "h2"
              className = "text-xl font-semibold text-slate-800 mt-5 mb-3 pb-1 border-b border-slate-100"
              break
            case 3:
              Component = "h3"
              className = "text-lg font-semibold text-slate-800 mt-4 mb-2"
              break
            case 4:
              Component = "h4"
              className = "text-base font-medium text-slate-700 mt-3 mb-2"
              break
            case 5:
              Component = "h5"
              className = "text-sm font-medium text-slate-700 mt-2 mb-1"
              break
            case 6:
              Component = "h6"
              className = "text-sm font-medium text-slate-600 mt-2 mb-1"
              break
          }

          elements.push(
            <div key={key++}>
              <Component className={className}>
                {formatInlineText(title)}
              </Component>
            </div>
          )
        }
        continue
      }

      // Main title (🎯 at start)
      if (trimmedLine.match(/^🎯\s+(.+)/)) {
        const title = trimmedLine.replace(/^🎯\s+/, '')
        elements.push(
          <div key={key++} className="mb-4">
            <h1 className="text-xl font-bold text-blue-900 flex items-center gap-2">
              <span>🎯</span>
              <span>{formatInlineText(title)}</span>
            </h1>
          </div>
        )
        continue
      }

      // Section headers with emojis (📊, 🔬, 💰, ⏰, ⚠️, 🧬, 🏆, 🤝, etc.)
      if (trimmedLine.match(/^[📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀🧬🏆🤝📚📈]\s+(.+)/)) {
        const match = trimmedLine.match(/^([📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀🧬🏆🤝📚📈])\s+(.+)/)
        if (match) {
          const [, emoji, title] = match
          const isIntelligentFeature = ['🧬', '🏆', '🤝', '📚', '📈'].includes(emoji)
          elements.push(
            <div key={key++} className="mt-6 mb-3">
              <h2 className={`text-lg font-semibold flex items-center gap-2 border-b pb-2 ${
                isIntelligentFeature 
                  ? 'text-blue-800 border-blue-200 bg-blue-50 px-3 py-2 rounded-t-lg' 
                  : 'text-slate-800 border-slate-200'
              }`}>
                <span className="text-xl">{emoji}</span>
                <span>{formatInlineText(title)}</span>
                {isIntelligentFeature && (
                  <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded-full ml-auto">
                    AI智能推荐
                  </span>
                )}
              </h2>
            </div>
          )
        }
        continue
      }

      // Subsection headers (🌟, 💡, 🎯, etc.)
      if (trimmedLine.match(/^[🌟💡🎯📅⚡]\s+(.+):/)) {
        const match = trimmedLine.match(/^([🌟💡🎯📅⚡])\s+(.+):?/)
        if (match) {
          const [, emoji, title] = match
          elements.push(
            <div key={key++} className="mt-4 mb-2">
              <h3 className="text-base font-medium text-slate-700 flex items-center gap-2">
                <span>{emoji}</span>
                <span>{formatInlineText(title)}</span>
              </h3>
            </div>
          )
        }
        continue
      }

      // Markdown分隔线支持 (--- *** ___)
      if (trimmedLine.match(/^(-{3,}|\*{3,}|_{3,})$/)) {
        elements.push(
          <div key={key++} className="my-4">
            <hr className="border-slate-300 border-t-2" />
          </div>
        )
        continue
      }

      // Separator lines (━━━)
      if (trimmedLine.match(/^━+$/)) {
        elements.push(
          <div key={key++} className="my-4">
            <hr className="border-slate-300 border-t-2" />
          </div>
        )
        continue
      }

      // Markdown无序列表支持 (- * +)
      if (trimmedLine.match(/^[\s]*[-\*\+]\s+(.+)/)) {
        const match = trimmedLine.match(/^(\s*)([-\*\+])\s+(.+)/)
        if (match) {
          const [, indent, bullet, text] = match
          const indentLevel = Math.floor(indent.length / 2) // 每2个空格为一级缩进
          const marginLeft = indentLevel * 20 + 16 // 基础16px + 每级20px

          elements.push(
            <div key={key++} className="mb-1 flex items-start gap-2" style={{ marginLeft: `${marginLeft}px` }}>
              <span className="mt-0.5 text-slate-600 font-bold">
                •
              </span>
              <span className="text-slate-700 leading-relaxed">{formatInlineText(text)}</span>
            </div>
          )
        }
        continue
      }

      // Bullet points (• or ✅)
      if (trimmedLine.match(/^[•✅]\s+(.+)/)) {
        const match = trimmedLine.match(/^([•✅])\s+(.+)/)
        if (match) {
          const [, bullet, text] = match
          const isCheckmark = bullet === '✅'
          elements.push(
            <div key={key++} className="ml-4 mb-1 flex items-start gap-2">
              <span className={`mt-0.5 ${isCheckmark ? 'text-green-600' : 'text-slate-600'}`}>
                {bullet}
              </span>
              <span className="text-slate-700 leading-relaxed">{formatInlineText(text)}</span>
            </div>
          )
        }
        continue
      }

      // Numbered lists (1., 2., etc.)
      if (trimmedLine.match(/^\d+\.\s+(.+)/)) {
        const match = trimmedLine.match(/^(\d+)\.\s+(.+)/)
        if (match) {
          const [, number, text] = match
          elements.push(
            <div key={key++} className="ml-4 mb-2 flex items-start gap-3">
              <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2 py-0.5 rounded-full min-w-[24px] text-center">
                {number}
              </span>
              <span className="text-slate-700 leading-relaxed">{formatInlineText(text)}</span>
            </div>
          )
        }
        continue
      }

      // Key-value pairs with colon (研究目标：xxx)
      if (trimmedLine.includes('：') && !trimmedLine.match(/^[📊🔬💰⏰⚠️🎯📋📅⚡🛡️📞🔍💡🚀]/)) {
        const parts = trimmedLine.split('：')
        if (parts.length === 2) {
          const keyText = parts[0].trim()
          const value = parts[1].trim()

          // Special styling for different types of information
          const isImportant = keyText.includes('预算') || keyText.includes('费用') || keyText.includes('成本')
          const isTime = keyText.includes('时间') || keyText.includes('周期') || keyText.includes('阶段')

          elements.push(
            <div key={key++} className="mb-2 flex items-start">
              <span className={`font-medium min-w-[120px] ${
                isImportant ? 'text-green-700' :
                isTime ? 'text-blue-700' :
                'text-slate-800'
              }`}>
                {keyText}：
              </span>
              <span className={`text-slate-700 ${
                isImportant ? 'font-medium text-green-800' : ''
              }`}>
                {formatInlineText(value)}
              </span>
            </div>
          )
          continue
        }
      }

      // Cost/fee information (¥ symbol)
      if (trimmedLine.includes('¥')) {
        // Extract cost ranges and highlight them
        const costPattern = /¥([\d,.-]+)/g
        const parts = trimmedLine.split(costPattern)
        const formattedParts = parts.map((part, index) => {
          if (part.match(/^[\d,.-]+$/)) {
            return (
              <span key={index} className="font-semibold text-green-700 bg-green-50 px-1 rounded">
                ¥{part}
              </span>
            )
          }
          return part
        })

        elements.push(
          <div key={key++} className="mb-1 text-slate-700">
            {formattedParts}
          </div>
        )
        continue
      }

      // 表格支持 (| column1 | column2 |)
      if (trimmedLine.includes('|') && trimmedLine.split('|').length >= 3) {
        // 收集表格行
        const tableRows: string[] = [trimmedLine]
        let j = i + 1

        // 收集连续的表格行
        while (j < lines.length) {
          const nextLine = lines[j].trim()
          if (nextLine.includes('|') && nextLine.split('|').length >= 3) {
            tableRows.push(nextLine)
            j++
          } else if (nextLine === '') {
            j++
            break
          } else {
            break
          }
        }

        if (tableRows.length > 0) {
          const isHeaderSeparator = (row: string) => /^[\|\s\-:]+$/.test(row)
          let headerRow: string | null = null
          let separatorIndex = -1
          let dataRows: string[] = []

          // 查找表头和分隔符
          for (let k = 0; k < tableRows.length; k++) {
            if (isHeaderSeparator(tableRows[k])) {
              if (k > 0) {
                headerRow = tableRows[k - 1]
                separatorIndex = k
              }
              break
            }
          }

          // 如果找到分隔符，分离数据行
          if (separatorIndex >= 0) {
            dataRows = tableRows.slice(separatorIndex + 1)
          } else {
            // 没有分隔符，第一行作为表头，其余作为数据
            headerRow = tableRows[0]
            dataRows = tableRows.slice(1)
          }

          const parseTableRow = (row: string) => {
            return row.split('|')
              .map(cell => cell.trim())
              .filter(cell => cell !== '')
          }

          elements.push(
            <div key={key++} className="my-4 overflow-x-auto">
              <table className="min-w-full border border-slate-200 rounded-lg overflow-hidden">
                {headerRow && (
                  <thead className="bg-slate-50">
                    <tr>
                      {parseTableRow(headerRow).map((header, index) => (
                        <th key={index} className="px-4 py-2 text-left text-sm font-medium text-slate-700 border-b border-slate-200">
                          {formatInlineText(header)}
                        </th>
                      ))}
                    </tr>
                  </thead>
                )}
                <tbody>
                  {dataRows.map((row, rowIndex) => (
                    <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-slate-25'}>
                      {parseTableRow(row).map((cell, cellIndex) => (
                        <td key={cellIndex} className="px-4 py-2 text-sm text-slate-700 border-b border-slate-100">
                          {formatInlineText(cell)}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )

          i = j - 1 // 跳过已处理的行
          continue
        }
      }

      // 引用块支持 (> text)
      if (trimmedLine.startsWith('> ')) {
        const quoteText = trimmedLine.slice(2)
        elements.push(
          <div key={key++} className="my-3 pl-4 border-l-4 border-blue-300 bg-blue-50 py-2">
            <p className="text-slate-700 italic">{formatInlineText(quoteText)}</p>
          </div>
        )
        continue
      }

      // 警告和提示框增强
      if (trimmedLine.match(/^[⚠️💡ℹ️✅]\s+(.+)/)) {
        const match = trimmedLine.match(/^([⚠️💡ℹ️✅])\s+(.+)/)
        if (match) {
          const [, emoji, text] = match
          let bgColor, borderColor, textColor, icon

          switch (emoji) {
            case '⚠️':
              bgColor = 'bg-amber-50'
              borderColor = 'border-amber-200'
              textColor = 'text-amber-800'
              icon = <AlertTriangle className="h-4 w-4" />
              break
            case '💡':
              bgColor = 'bg-blue-50'
              borderColor = 'border-blue-200'
              textColor = 'text-blue-800'
              icon = <Lightbulb className="h-4 w-4" />
              break
            case 'ℹ️':
              bgColor = 'bg-slate-50'
              borderColor = 'border-slate-200'
              textColor = 'text-slate-800'
              icon = <Info className="h-4 w-4" />
              break
            case '✅':
              bgColor = 'bg-green-50'
              borderColor = 'border-green-200'
              textColor = 'text-green-800'
              icon = <CheckCircle className="h-4 w-4" />
              break
            default:
              bgColor = 'bg-slate-50'
              borderColor = 'border-slate-200'
              textColor = 'text-slate-800'
              icon = null
          }

          elements.push(
            <div key={key++} className={`p-3 rounded-lg mb-3 ${bgColor} ${borderColor} border`}>
              <div className={`font-medium flex items-center gap-2 ${textColor}`}>
                {icon}
                <span>{formatInlineText(text)}</span>
              </div>
            </div>
          )
        }
        continue
      }

      // Signature line (CellForge AI - ...)
      if (trimmedLine.includes('CellForge AI') && trimmedLine.includes('🧬')) {
        elements.push(
          <div key={key++} className="mt-6 pt-4 border-t border-slate-200">
            <p className="text-center text-sm text-slate-500 italic">
              {trimmedLine}
            </p>
          </div>
        )
        continue
      }

      // 检测文献引用格式 **[数字] 标题**
      const literatureMatch = trimmedLine.match(/^\*\*\[(\d+)\]\s+(.+?)\*\*$/)
      if (literatureMatch) {
        const [, number, title] = literatureMatch

        // 收集后续的文献信息行
        const literatureInfo: string[] = [trimmedLine]
        let j = i + 1

        // 收集文献的详细信息（作者、期刊、DOI等）
        while (j < lines.length) {
          const nextLine = lines[j].trim()
          if (nextLine === '' || nextLine.startsWith('**[')) {
            break
          }
          if (nextLine.startsWith('*') ||
              nextLine.startsWith('📖') ||
              nextLine.startsWith('🏆') ||
              nextLine.startsWith('💡') ||
              nextLine.startsWith('🔑') ||
              nextLine.startsWith('🔗')) {
            literatureInfo.push(nextLine)
            j++
          } else {
            break
          }
        }

        // 解析文献信息
        const literature = parseLiteratureInfo(literatureInfo)
        if (literature) {
          elements.push(
            <div key={key++} className="my-4">
              <InteractiveLiteratureCitation
                literature={literature}
                relevanceExplanation={literature.relevanceExplanation}
                supportPoints={literature.supportPoints}
                showQualityIndicators={true}
              />
            </div>
          )
        } else {
          // 如果解析失败，回退到普通文本显示
          elements.push(
            <p key={key++} className="mb-2 text-slate-700 leading-relaxed">
              {formatInlineText(trimmedLine)}
            </p>
          )
        }

        i = j - 1 // 跳过已处理的行
        continue
      }

      // Regular paragraph text
      if (trimmedLine.length > 0) {
        // Check if it's a continuation of a list or section
        const isIndented = line.startsWith('  ') || line.startsWith('\t')

        elements.push(
          <p key={key++} className={`mb-2 text-slate-700 leading-relaxed ${
            isIndented ? 'ml-4 text-slate-600' : ''
          }`}>
            {formatInlineText(trimmedLine)}
          </p>
        )
      }
    }

    return elements
  }

  return (
    <div className={`formatted-message ${className}`}>
      {formatContent(content)}
    </div>
  )
}

// 文献信息解析函数
function parseLiteratureInfo(literatureInfo: string[]): any | null {
  try {
    const titleLine = literatureInfo[0]
    const titleMatch = titleLine.match(/^\*\*\[(\d+)\]\s+(.+?)\*\*$/)
    if (!titleMatch) return null

    const [, number, title] = titleMatch

    // 初始化文献对象
    const literature: any = {
      id: parseInt(number),
      title: title,
      authors: [],
      journal: '',
      publication_year: new Date().getFullYear(),
      category: 'unknown',
      technology_tags: [],
      application_tags: [],
      citation_count: 0,
      relevance_score: 0,
      key_findings: '',
      methodology_summary: '',
      business_value: '',
      abstract: '',
      relevanceExplanation: '',
      supportPoints: []
    }

    // 解析其他信息行
    for (let i = 1; i < literatureInfo.length; i++) {
      const line = literatureInfo[i].trim()

      // 作者和期刊信息 *作者等*
      if (line.startsWith('*') && line.endsWith('*') && !line.includes('**')) {
        const authorJournalMatch = line.match(/^\*(.+?)\*$/)
        if (authorJournalMatch) {
          const authorJournalText = authorJournalMatch[1]
          const parts = authorJournalText.split('等')
          if (parts.length > 0) {
            literature.authors = parts[0].split(',').map((author: string) => author.trim())
          }
        }
      }

      // 期刊信息 📖 期刊名 (年份)
      else if (line.startsWith('📖')) {
        const journalMatch = line.match(/📖\s+(.+?)\s+\((\d{4})\)/)
        if (journalMatch) {
          literature.journal = journalMatch[1]
          literature.publication_year = parseInt(journalMatch[2])
        }
      }

      // 影响因子 🏆 影响因子: 数字
      else if (line.startsWith('🏆')) {
        const ifMatch = line.match(/🏆\s+影响因子:\s*([\d.]+)/)
        if (ifMatch) {
          literature.impact_factor = parseFloat(ifMatch[1])
        }
      }

      // 相关性说明 💡 **相关性**: 内容
      else if (line.startsWith('💡')) {
        const relevanceMatch = line.match(/💡\s+\*\*相关性\*\*:\s*(.+)/)
        if (relevanceMatch) {
          literature.relevanceExplanation = relevanceMatch[1]
        }
      }

      // 支持要点 🔑 **支持要点**: 内容
      else if (line.startsWith('🔑')) {
        const supportMatch = line.match(/🔑\s+\*\*支持要点\*\*:\s*(.+)/)
        if (supportMatch) {
          literature.supportPoints.push(supportMatch[1])
        }
      }

      // DOI链接 🔗 DOI: 内容
      else if (line.startsWith('🔗')) {
        const doiMatch = line.match(/🔗\s+DOI:\s*(.+)/)
        if (doiMatch) {
          literature.doi = doiMatch[1]
        }
      }
    }

    // 设置默认值
    if (!literature.key_findings) {
      literature.key_findings = `${title}的重要研究发现`
    }

    if (!literature.methodology_summary) {
      literature.methodology_summary = '详细的方法学信息请查看原文'
    }

    if (!literature.business_value) {
      literature.business_value = '为单细胞测序研究提供重要的理论和技术支持'
    }

    // 设置技术标签（基于标题推断）
    const titleLower = title.toLowerCase()
    if (titleLower.includes('single-cell') || titleLower.includes('scrna')) {
      literature.technology_tags.push('scRNA-seq')
    }
    if (titleLower.includes('atac')) {
      literature.technology_tags.push('scATAC-seq')
    }
    if (titleLower.includes('seurat')) {
      literature.technology_tags.push('seurat')
    }
    if (titleLower.includes('10x')) {
      literature.technology_tags.push('10x_genomics')
    }

    // 设置应用标签
    if (titleLower.includes('immune') || titleLower.includes('dendritic')) {
      literature.application_tags.push('immunology')
    }
    if (titleLower.includes('development')) {
      literature.application_tags.push('development')
    }

    // 设置分类
    if (titleLower.includes('integration') || titleLower.includes('method')) {
      literature.category = 'methodology'
    } else if (titleLower.includes('reveals') || titleLower.includes('analysis')) {
      literature.category = 'application'
    } else {
      literature.category = 'technology'
    }

    // 估算引用数和相关性评分
    if (literature.impact_factor) {
      literature.citation_count = Math.round(literature.impact_factor * 100 + Math.random() * 1000)
      literature.relevance_score = Math.min(0.95, literature.impact_factor / 50 + 0.5)
    } else {
      literature.citation_count = Math.round(Math.random() * 500 + 100)
      literature.relevance_score = 0.7 + Math.random() * 0.2
    }

    return literature
  } catch (error) {
    console.error('解析文献信息失败:', error)
    return null
  }
}
