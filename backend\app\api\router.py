"""
API路由汇总
"""
from fastapi import APIRouter

from app.api.endpoints import conversation, auth, literature, customer_profile, web_profile_analysis, smart_literature, solution_generator, intelligent_recommendation, enhanced_solution, comprehensive_solution

# 创建主路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    conversation.router,
    prefix="/conversation",
    tags=["conversation"]
)

api_router.include_router(
    literature.router,
    prefix="/literature",
    tags=["literature"]
)

api_router.include_router(
    customer_profile.router,
    prefix="/customer",
    tags=["customer_profile"]
)

api_router.include_router(
    web_profile_analysis.router,
    prefix="/profile-analysis",
    tags=["profile_analysis"]
)

api_router.include_router(
    smart_literature.router,
    prefix="/smart-literature",
    tags=["smart_literature"]
)

api_router.include_router(
    solution_generator.router,
    prefix="/solution",
    tags=["solution_generator"]
)

api_router.include_router(
    intelligent_recommendation.router,
    prefix="/intelligent-recommendation", 
    tags=["intelligent_recommendation"]
)

api_router.include_router(
    enhanced_solution.router,
    prefix="/enhanced-solution",
    tags=["enhanced_solution"]
)

api_router.include_router(
    comprehensive_solution.router,
    prefix="/comprehensive-solution",
    tags=["comprehensive_solution"]
)

# 健康检查端点
@api_router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "cellforge-ai-backend",
        "version": "1.0.0"
    }
