"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   conversationApi: () => (/* binding */ conversationApi),\n/* harmony export */   customerProfileApi: () => (/* binding */ customerProfileApi),\n/* harmony export */   intelligentRecommendationApi: () => (/* binding */ intelligentRecommendationApi),\n/* harmony export */   literatureApi: () => (/* binding */ literatureApi),\n/* harmony export */   smartLiteratureApi: () => (/* binding */ smartLiteratureApi),\n/* harmony export */   solutionApi: () => (/* binding */ solutionApi)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * API调用工具函数\n */ // 动态获取API基础URL\nconst getApiBaseUrl = ()=>{\n    // 优先使用环境变量\n    if (process.env.NEXT_PUBLIC_API_URL) {\n        return process.env.NEXT_PUBLIC_API_URL;\n    }\n    // 在浏览器环境中，使用当前主机的IP和端口8000\n    if (true) {\n        const protocol = window.location.protocol;\n        const hostname = window.location.hostname;\n        return \"\".concat(protocol, \"//\").concat(hostname, \":8000/api/v1\");\n    }\n    // 服务端渲染时的默认值\n    return 'http://localhost:8000/api/v1';\n};\nconst API_BASE_URL = getApiBaseUrl();\n// Token管理\nclass TokenManager {\n    static getToken() {\n        if (false) {}\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static setToken(token) {\n        if (false) {}\n        localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    static removeToken() {\n        if (false) {}\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_KEY);\n    }\n    static isTokenExpired(token) {\n        try {\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            return payload.exp * 1000 < Date.now();\n        } catch (e) {\n            return true;\n        }\n    }\n}\nTokenManager.TOKEN_KEY = 'cellforge_access_token';\nTokenManager.REFRESH_KEY = 'cellforge_refresh_token';\n// HTTP客户端类\nclass ApiClient {\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const token = TokenManager.getToken();\n        const headers = {\n            'Content-Type': 'application/json',\n            ...options.headers\n        };\n        // 添加认证头\n        if (token && !TokenManager.isTokenExpired(token)) {\n            headers.Authorization = \"Bearer \".concat(token);\n        }\n        const config = {\n            ...options,\n            headers\n        };\n        try {\n            const response = await fetch(url, config);\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                // 根据HTTP状态码提供更详细的错误信息\n                let errorMessage = errorData.detail || errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n                if (response.status === 401) {\n                    errorMessage = errorData.detail || \"邮箱或密码错误\";\n                } else if (response.status === 403) {\n                    errorMessage = errorData.detail || \"账户已被暂停或无权限访问\";\n                } else if (response.status === 404) {\n                    errorMessage = \"请求的资源不存在\";\n                } else if (response.status === 422) {\n                    errorMessage = \"请求数据格式错误\";\n                } else if (response.status >= 500) {\n                    errorMessage = \"服务器内部错误，请稍后重试\";\n                }\n                throw new Error(errorMessage);\n            }\n            return await response.json();\n        } catch (error) {\n            // 网络错误处理\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                throw new Error('网络连接失败，请检查网络连接');\n            }\n            console.error('API请求失败:', error);\n            throw error;\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        return this.request(endpoint, {\n            method: 'GET'\n        });\n    }\n    // POST请求\n    async post(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        });\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        return this.request(endpoint, {\n            method: 'DELETE'\n        });\n    }\n    constructor(baseURL = API_BASE_URL){\n        this.baseURL = baseURL;\n    }\n}\n// 创建API客户端实例\nconst apiClient = new ApiClient();\n// 认证API\nconst authApi = {\n    // 用户登录\n    async login (credentials) {\n        const response = await apiClient.post('/auth/login', credentials);\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 用户注册\n    async register (userData) {\n        return apiClient.post('/auth/register', userData);\n    },\n    // 获取当前用户信息\n    async getCurrentUser () {\n        return apiClient.get('/auth/me');\n    },\n    // 刷新令牌\n    async refreshToken () {\n        const response = await apiClient.post('/auth/refresh');\n        TokenManager.setToken(response.access_token);\n        return response;\n    },\n    // 修改密码\n    async changePassword (data) {\n        return apiClient.post('/auth/change-password', data);\n    },\n    // 登出\n    logout () {\n        TokenManager.removeToken();\n    }\n};\n// 对话API\nconst conversationApi = {\n    // 发送消息\n    async sendMessage (data) {\n        return apiClient.post('/conversation/chat', data);\n    },\n    // 获取对话历史\n    async getHistory () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, size = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        return apiClient.get(\"/conversation/history?page=\".concat(page, \"&size=\").concat(size));\n    }\n};\n// 文献API\nconst literatureApi = {\n    // 搜索文献\n    async searchLiterature (data) {\n        return apiClient.post('/literature/search', data);\n    },\n    // 获取文献推荐\n    async getLiteratureRecommendations (data) {\n        return apiClient.post('/literature/recommendations', data);\n    },\n    // 获取文献分类\n    async getCategories () {\n        return apiClient.get('/literature/categories');\n    },\n    // 获取热门文献\n    async getTrendingLiterature () {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return apiClient.get(\"/literature/trending?limit=\".concat(limit));\n    },\n    // 获取文献统计\n    async getStats () {\n        return apiClient.get('/literature/stats');\n    },\n    // 获取文献搜索状态\n    async getSearchStatus () {\n        return apiClient.get('/literature/search-status');\n    },\n    // 基于需求搜集文献\n    async collectForRequirements (data) {\n        return apiClient.post('/literature/collect-for-requirements', data);\n    }\n};\n// 客户画像API\nconst customerProfileApi = {\n    // 获取客户画像\n    async getProfile (userId) {\n        return apiClient.get(\"/customer/profile/\".concat(userId));\n    },\n    // 分析客户画像\n    async analyzeProfile (data) {\n        return apiClient.post('/customer/analyze', data);\n    },\n    // 获取客户洞察\n    async getInsights (userId) {\n        return apiClient.get(\"/customer/insights/\".concat(userId));\n    },\n    // 获取个性化推荐\n    async getRecommendations (userId) {\n        return apiClient.get(\"/customer/recommendations/\".concat(userId));\n    },\n    // 更新客户画像\n    async updateProfile (userId, profileData) {\n        return apiClient.put(\"/customer/profile/\".concat(userId), profileData);\n    },\n    // 基于需求更新画像\n    async updateFromRequirements (userId, requirementData) {\n        return apiClient.post(\"/customer/requirements/\".concat(userId), requirementData);\n    },\n    // 网页分析\n    async analyzeWebProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-url', data);\n    },\n    // 文本分析\n    async analyzeTextProfile (data) {\n        return apiClient.post('/profile-analysis/analyze-text', data);\n    },\n    // 获取分析历史\n    async getAnalysisHistory () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, size = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        return apiClient.get(\"/profile-analysis/analysis-history?page=\".concat(page, \"&size=\").concat(size));\n    }\n};\n// 智能文献搜索API\nconst smartLiteratureApi = {\n    // 生成智能搜索查询\n    async generateSmartQueries (data) {\n        return apiClient.post('/smart-literature/generate-smart-queries', data);\n    },\n    // Perplexity风格搜索\n    async perplexitySearch (data) {\n        return apiClient.post('/smart-literature/perplexity-search', data);\n    },\n    // 增强文献搜索\n    async enhancedLiteratureSearch (data) {\n        return apiClient.post('/smart-literature/enhanced-literature-search', data);\n    }\n};\n// 方案生成API\nconst solutionApi = {\n    // 生成完整方案\n    async generateSolution (data) {\n        return apiClient.post('/solution/generate-solution', data);\n    },\n    // 快速成本估算\n    async quickEstimate (requirements) {\n        return apiClient.post('/solution/quick-estimate', requirements);\n    },\n    // 获取方案模板\n    async getSolutionTemplates () {\n        return apiClient.get('/solution/solution-templates');\n    },\n    // 验证需求完整性\n    async validateRequirements (requirements) {\n        return apiClient.post('/solution/validate-requirements', requirements);\n    },\n    // 获取平台对比\n    async getPlatformComparison () {\n        return apiClient.get('/solution/platform-comparison');\n    },\n    // 生成综合方案框架（按设计文档要求）\n    async generateComprehensiveFramework (data) {\n        return apiClient.post('/solution/comprehensive-framework', {\n            requirements: data.requirements,\n            user_message: data.user_message || '',\n            framework_template: data.framework_template || 'standard',\n            enable_literature_search: data.enable_literature_search !== false\n        });\n    }\n};\n// 智能推荐API\nconst intelligentRecommendationApi = {\n    // 获取综合智能推荐\n    async getComprehensiveRecommendation (data) {\n        return apiClient.post('/intelligent-recommendation/comprehensive-recommendation', data);\n    },\n    // 获取智能关键词扩展\n    async getKeywordExpansion (data) {\n        return apiClient.post('/intelligent-recommendation/keyword-expansion', data);\n    },\n    // 获取热点文献推荐\n    async getHotPapers (data) {\n        return apiClient.post('/intelligent-recommendation/hot-papers', data);\n    },\n    // 获取技术平台推荐\n    async getTechRecommendations (data) {\n        return apiClient.post('/intelligent-recommendation/tech-recommendations', data);\n    },\n    // 获取项目整体解决方案\n    async getProjectSolution (data) {\n        return apiClient.post('/intelligent-recommendation/project-solution', data);\n    },\n    // 健康检查\n    async healthCheck () {\n        return apiClient.get('/intelligent-recommendation/health');\n    }\n};\n// 导出Token管理器和API客户端\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});