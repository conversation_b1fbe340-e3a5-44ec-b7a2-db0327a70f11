"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/conversation-interface.tsx":
/*!***********************************************!*\
  !*** ./components/conversation-interface.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationInterface: () => (/* binding */ ConversationInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/paperclip.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Brain,ChevronLeft,ChevronRight,ClipboardList,History,MessageSquare,Mic,Paperclip,RotateCcw,Send,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/enhanced-error-handler */ \"(app-pages-browser)/./lib/enhanced-error-handler.ts\");\n/* harmony import */ var _enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./enhanced-loading-indicator */ \"(app-pages-browser)/./components/enhanced-loading-indicator.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _progressive_requirement_collector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./progressive-requirement-collector */ \"(app-pages-browser)/./components/progressive-requirement-collector.tsx\");\n/* harmony import */ var _formatted_message__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./formatted-message */ \"(app-pages-browser)/./components/formatted-message.tsx\");\n/* harmony import */ var _conversation_history__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./conversation-history */ \"(app-pages-browser)/./components/conversation-history.tsx\");\n/* harmony import */ var _literature_search_toggle__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./literature-search-toggle */ \"(app-pages-browser)/./components/literature-search-toggle.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConversationInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ConversationInterface() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [conversationId, setConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [requirements, setRequirements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [rightPanelCollapsed, setRightPanelCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [resetTrigger, setResetTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0) // 用于触发需求收集助手重置\n    ;\n    const [enableLiteratureSearch, setEnableLiteratureSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 文献搜索开关\n    ;\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // 使用增强的加载状态管理\n    const { isLoading, currentStage, progress, startLoading, updateStage, finishLoading, cancelLoading } = (0,_enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.useStageLoading)();\n    // 响应式检测\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ConversationInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 1024);\n                    if (window.innerWidth < 1024) {\n                        setRightPanelCollapsed(true);\n                    }\n                }\n            }[\"ConversationInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ConversationInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ConversationInterface.useEffect\"];\n        }\n    }[\"ConversationInterface.useEffect\"], []);\n    // 滚动到底部\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ConversationInterface.useEffect\"], [\n        messages\n    ]);\n    // 初始化欢迎消息\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConversationInterface.useEffect\": ()=>{\n            const welcomeMessage = {\n                id: \"welcome\",\n                type: \"ai\",\n                content: \"您好\".concat((user === null || user === void 0 ? void 0 : user.name) ? \"，\".concat(user.name) : '', \"！\\uD83D\\uDC4B\\n\\n我是 **CellForge AI** 智能顾问，专注于单细胞测序技术咨询。\\n\\n\\uD83D\\uDD2C **我的专长**：\\n• 单细胞RNA测序 (scRNA-seq)\\n• 单细胞ATAC测序 (scATAC-seq)\\n• 多组学测序 (Multiome)\\n• 空间转录组学\\n• 实验设计与数据分析\\n\\n\\uD83D\\uDCA1 **开始方式**：\\n您可以直接提问，我会智能收集您的需求信息，或者使用右侧的需求收集助手快速填写项目信息。\\n\\n请告诉我您的研究目标，我来为您制定最佳的技术方案！\"),\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: 1.0,\n                suggestions: [\n                    \"我需要进行单细胞RNA测序\",\n                    \"请推荐适合的技术平台\",\n                    \"帮我分析项目成本\",\n                    \"查看成功案例\"\n                ]\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n        }\n    }[\"ConversationInterface.useEffect\"], [\n        user\n    ]);\n    // 自动保存对话到历史\n    const autoSaveConversation = (messages)=>{\n        if (messages.length >= 2) {\n            try {\n                var _existingSessions_find, _messages_;\n                const storageKey = \"cellforge_conversations_\".concat((user === null || user === void 0 ? void 0 : user.id) || 'anonymous');\n                const existingSessions = JSON.parse(localStorage.getItem(storageKey) || '[]');\n                // 生成对话标题\n                const generateTitle = (msgs)=>{\n                    const firstUserMessage = msgs.find((m)=>m.type === \"user\");\n                    if (firstUserMessage) {\n                        const content = firstUserMessage.content.trim();\n                        return content.length > 30 ? content.substring(0, 30) + \"...\" : content;\n                    }\n                    return \"新对话\";\n                };\n                const sessionId = currentSessionId || \"session_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n                const now = new Date();\n                const session = {\n                    id: sessionId,\n                    title: generateTitle(messages),\n                    messages,\n                    createdAt: currentSessionId ? ((_existingSessions_find = existingSessions.find((s)=>s.id === sessionId)) === null || _existingSessions_find === void 0 ? void 0 : _existingSessions_find.createdAt) || now : now,\n                    updatedAt: now,\n                    isStarred: false,\n                    isArchived: false,\n                    tags: [],\n                    messageCount: messages.length,\n                    lastMessage: ((_messages_ = messages[messages.length - 1]) === null || _messages_ === void 0 ? void 0 : _messages_.content.substring(0, 100)) || \"\"\n                };\n                // 更新或添加会话\n                const updatedSessions = currentSessionId ? existingSessions.map((s)=>s.id === sessionId ? session : s) : [\n                    session,\n                    ...existingSessions\n                ].slice(0, 50) // 最多保存50个对话\n                ;\n                localStorage.setItem(storageKey, JSON.stringify(updatedSessions));\n                setCurrentSessionId(sessionId);\n            } catch (error) {\n                console.error('自动保存对话失败:', error);\n            }\n        }\n    };\n    // 处理表单提交 - 使用增强的错误处理和分阶段加载\n    const handleSendMessage = async ()=>{\n        if (!inputValue.trim()) return;\n        const userMessage = inputValue.trim();\n        setInputValue(\"\");\n        setError(\"\");\n        // 创建用户消息\n        const newUserMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: userMessage,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newUserMessage\n            ]);\n        // 构建对话历史和上下文（在分阶段操作外部准备）\n        const conversationHistory = messages.map((msg)=>({\n                role: msg.type === \"user\" ? \"user\" : \"assistant\",\n                content: msg.content\n            }));\n        const enhancedContext = {\n            user_profile: {\n                id: user === null || user === void 0 ? void 0 : user.id,\n                organization: (user === null || user === void 0 ? void 0 : user.organization) || \"\",\n                role: (user === null || user === void 0 ? void 0 : user.role) || \"\",\n                expertise: (user === null || user === void 0 ? void 0 : user.expertise_areas) || \"\",\n                research_interests: (user === null || user === void 0 ? void 0 : user.research_interests) || \"\"\n            },\n            requirements: requirements,\n            conversation_history: conversationHistory,\n            enable_literature_search: enableLiteratureSearch\n        };\n        try {\n            // 使用分阶段加载处理\n            let aiApiResponse;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        // 模拟分析阶段\n                        await new Promise((resolve)=>setTimeout(resolve, 800));\n                        return {\n                            step: \"analyzed\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.SEARCHING_LITERATURE,\n                    operation: async ()=>{\n                        // 模拟文献搜索\n                        await new Promise((resolve)=>setTimeout(resolve, 1200));\n                        return {\n                            step: \"literature_searched\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        // 实际调用AI API\n                        aiApiResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.conversationApi.sendMessage({\n                            message: userMessage,\n                            conversation_id: conversationId || undefined,\n                            conversation_type: \"enhanced\",\n                            history: conversationHistory,\n                            context: enhancedContext\n                        });\n                        return aiApiResponse;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.FORMATTING,\n                    operation: async ()=>{\n                        // 模拟格式化处理\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        return {\n                            step: \"formatted\"\n                        };\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"AI服务暂时不可用，请稍后重试\",\n                showToast: false,\n                onRetry: (attempt, error)=>{\n                    var _error_message, _error_message1;\n                    // 只在网络错误时才显示重试信息\n                    if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('fetch')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('网络'))) {\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"检测到网络问题，正在重试...\");\n                    }\n                }\n            });\n            // aiApiResponse已经在GENERATING_RESPONSE阶段中设置\n            // 更新用户消息状态为已发送\n            setMessages((prev)=>prev.map((msg)=>msg.id === newUserMessage.id ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n            // 创建AI回复消息\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: aiApiResponse.message,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: aiApiResponse.confidence,\n                sources: aiApiResponse.sources,\n                suggestions: aiApiResponse.suggestions\n            };\n            setMessages((prev)=>{\n                const newMessages = [\n                    ...prev,\n                    aiResponse\n                ];\n                // 自动保存对话\n                setTimeout(()=>autoSaveConversation(newMessages), 1000);\n                return newMessages;\n            });\n            finishLoading();\n        } catch (err) {\n            console.error(\"发送消息失败:\", err);\n            // 更新用户消息状态为错误\n            setMessages((prev)=>prev.map((msg)=>msg.id === newUserMessage.id ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            // 错误已经由errorHandler处理，这里只需要更新UI状态\n            setError(\"发送消息失败，请稍后重试\");\n            cancelLoading();\n        }\n    };\n    // 处理需求变更\n    const handleRequirementsChange = (newRequirements)=>{\n        setRequirements(newRequirements);\n    };\n    // 处理需求提交 - 使用新的方案生成API\n    const handleRequirementsSubmit = async (requirements)=>{\n        // 生成需求总结消息\n        const requirementSummary = generateRequirementSummary(requirements);\n        // 添加用户消息到对话中\n        const userMessage = {\n            id: Date.now().toString(),\n            type: \"user\",\n            content: requirementSummary,\n            timestamp: new Date(),\n            status: \"sending\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        try {\n            var _solutionResponse_ai_analysis_features, _solutionResponse_solution_id, _solutionResponse_ai_analysis_features1;\n            // 使用分阶段加载处理需求提交，但现在专注于方案生成\n            let solutionResponse;\n            let literatureResults = null;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        // 验证需求完整性\n                        const validation = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.solutionApi.validateRequirements(requirements);\n                        return validation;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.SEARCHING_LITERATURE,\n                    operation: async ()=>{\n                        // 如果启用了文献搜索，先执行文献搜索获得可见的结果\n                        if (enableLiteratureSearch) {\n                            try {\n                                // 调用智能文献搜索API获得用户可见的搜索过程\n                                const literatureResult = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.literatureApi.enhancedLiteratureSearch({\n                                    query: \"单细胞测序 \".concat(requirements.researchGoal, \" \").concat(requirements.sampleType),\n                                    requirements: requirements,\n                                    enable_literature_search: true\n                                });\n                                // 保存文献搜索结果以便后续使用\n                                literatureResults = literatureResult;\n                                return {\n                                    step: \"literature_evidence_collected\",\n                                    literature_results: literatureResult,\n                                    search_visible: true\n                                };\n                            } catch (error) {\n                                console.warn(\"文献搜索失败，继续方案生成:\", error);\n                                // 搜索失败也继续流程，在方案生成时会有内部文献支撑\n                                return {\n                                    step: \"literature_fallback_mode\"\n                                };\n                            }\n                        } else {\n                            // 文献搜索在后端内部进行（用户不可见）\n                            await new Promise((resolve)=>setTimeout(resolve, 800));\n                            return {\n                                step: \"literature_evidence_collected\",\n                                search_visible: false\n                            };\n                        }\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        // 优先调用综合方案框架API（按设计文档要求）\n                        try {\n                            console.log('🚀 开始调用综合方案框架API...');\n                            console.log('请求数据:', {\n                                requirements: requirements,\n                                user_message: requirementSummary,\n                                framework_template: 'standard',\n                                enable_literature_search: enableLiteratureSearch\n                            });\n                            const frameworkResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.solutionApi.generateComprehensiveFramework({\n                                requirements: requirements,\n                                user_message: requirementSummary,\n                                framework_template: 'standard',\n                                enable_literature_search: enableLiteratureSearch\n                            });\n                            console.log('✅ 综合方案框架API响应:', frameworkResponse);\n                            if (frameworkResponse.success) {\n                                var _frameworkResponse_data_solution_overview, _frameworkResponse_data_implementation_plan;\n                                // 将综合方案框架结果转换为前端需要的格式\n                                solutionResponse = {\n                                    solution_id: frameworkResponse.framework_id,\n                                    generated_at: frameworkResponse.generation_time,\n                                    client_requirements: requirements,\n                                    comprehensive_framework: frameworkResponse.data,\n                                    recommended_solution: {\n                                        platform: \"CellForge AI 综合方案框架\",\n                                        reasoning: \"基于设计文档的完整方案框架，包含方案概览、研究意图分析、关键要素、文献推荐、平台对比和实施规划\",\n                                        specifications: frameworkResponse.data.solution_overview\n                                    },\n                                    cost_analysis: ((_frameworkResponse_data_solution_overview = frameworkResponse.data.solution_overview) === null || _frameworkResponse_data_solution_overview === void 0 ? void 0 : _frameworkResponse_data_solution_overview.cost_analysis) || {},\n                                    risk_assessment: frameworkResponse.data.risk_assessment || {},\n                                    timeline: ((_frameworkResponse_data_implementation_plan = frameworkResponse.data.implementation_plan) === null || _frameworkResponse_data_implementation_plan === void 0 ? void 0 : _frameworkResponse_data_implementation_plan.timeline) || {},\n                                    deliverables: [\n                                        \"方案概览卡片\",\n                                        \"研究意图精准搜索链接\",\n                                        \"关键要素分析\",\n                                        \"智能文献推荐\",\n                                        \"平台对比分析\",\n                                        \"项目实施规划\"\n                                    ],\n                                    next_steps: [\n                                        \"查看完整的方案框架分析\",\n                                        \"使用精准搜索链接获取文献\",\n                                        \"根据平台对比选择技术方案\"\n                                    ],\n                                    contact_info: {\n                                        email: \"<EMAIL>\",\n                                        phone: \"************\"\n                                    },\n                                    framework_features: {\n                                        comprehensive_framework: true,\n                                        research_intent_analysis: true,\n                                        precision_search_links: true,\n                                        literature_recommendations: true,\n                                        platform_comparison: true\n                                    }\n                                };\n                            } else {\n                                throw new Error('综合方案框架API调用失败');\n                            }\n                        } catch (frameworkError) {\n                            console.warn('综合方案框架失败，使用降级方案:', frameworkError);\n                            // 使用简化的降级方案\n                            solutionResponse = {\n                                solution_id: \"fallback_\".concat(Date.now()),\n                                generated_at: new Date().toISOString(),\n                                client_requirements: requirements,\n                                recommended_solution: {\n                                    platform: \"CellForge AI 基础方案\",\n                                    reasoning: \"由于技术原因，当前提供基础方案。我们的专家将为您进行人工分析，确保方案的准确性和个性化。\",\n                                    specifications: {}\n                                },\n                                cost_analysis: {},\n                                risk_assessment: {},\n                                timeline: {},\n                                deliverables: [\n                                    \"人工专家分析报告\",\n                                    \"个性化技术方案设计\",\n                                    \"详细成本效益分析\"\n                                ],\n                                next_steps: [\n                                    \"联系专家进行人工分析\",\n                                    \"获取个性化方案设计\",\n                                    \"确定最优技术路线\"\n                                ],\n                                contact_info: {\n                                    email: \"<EMAIL>\",\n                                    phone: \"************\"\n                                },\n                                fallback_notice: {\n                                    message: \"为确保方案质量，建议联系专家进行人工分析\",\n                                    expert_contact: true,\n                                    reason: \"技术服务暂时不可用\"\n                                }\n                            };\n                        }\n                        return solutionResponse;\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.FORMATTING,\n                    operation: async ()=>{\n                        // 格式化方案内容，如果有文献结果则包含进去\n                        await new Promise((resolve)=>setTimeout(resolve, 400));\n                        return {\n                            step: \"solution_formatted\"\n                        };\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"方案生成失败，请稍后重试\",\n                showToast: false,\n                onRetry: (attempt, error)=>{\n                    var _error_message, _error_message1;\n                    if (((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('fetch')) || ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes('网络'))) {\n                        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"网络问题，正在重试生成方案...\");\n                    }\n                }\n            });\n            // 更新用户消息状态\n            setMessages((prev)=>prev.map((msg)=>msg.id === userMessage.id ? {\n                        ...msg,\n                        status: \"sent\"\n                    } : msg));\n            // 生成方案展示的AI回复，包含文献搜索结果（如果有）\n            const solutionContent = formatSolutionResponse(solutionResponse, literatureResults);\n            // 更新sources以反映实际的数据来源\n            let sources = [\n                \"方案生成系统\",\n                \"技术知识库\",\n                \"成本分析引擎\"\n            ];\n            // 根据不同的方案类型使用不同的数据来源\n            if ((_solutionResponse_ai_analysis_features = solutionResponse.ai_analysis_features) === null || _solutionResponse_ai_analysis_features === void 0 ? void 0 : _solutionResponse_ai_analysis_features.intent_analyzed) {\n                sources = [\n                    \"AI意图分析引擎\",\n                    \"用户行为分析\",\n                    \"个性化算法\",\n                    \"专业知识库\"\n                ];\n                if (solutionResponse.ai_analysis_features.analysis_sources) {\n                    sources = sources.concat(solutionResponse.ai_analysis_features.analysis_sources);\n                }\n            } else if (((_solutionResponse_solution_id = solutionResponse.solution_id) === null || _solutionResponse_solution_id === void 0 ? void 0 : _solutionResponse_solution_id.includes('intelligent')) || solutionResponse.intelligent_features) {\n                sources = [\n                    \"AI智能推荐引擎\",\n                    \"专业知识库\",\n                    \"热点文献发现\",\n                    \"技术规格数据库\",\n                    \"风险评估算法\"\n                ];\n            } else if (solutionResponse.fallback_notice) {\n                sources = [\n                    \"基础知识库\",\n                    \"专家经验库\",\n                    \"标准方案模板\"\n                ];\n            }\n            if (literatureResults && enableLiteratureSearch) {\n                sources.push(\"文献数据库\", \"智能搜索引擎\");\n            }\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                type: \"ai\",\n                content: solutionContent,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: ((_solutionResponse_ai_analysis_features1 = solutionResponse.ai_analysis_features) === null || _solutionResponse_ai_analysis_features1 === void 0 ? void 0 : _solutionResponse_ai_analysis_features1.confidence_score) || (literatureResults ? 0.98 : solutionResponse.fallback_notice ? 0.70 : 0.95),\n                sources: sources,\n                suggestions: [\n                    \"查看详细的成本分析\",\n                    \"了解项目时间规划\",\n                    \"获取技术平台对比\",\n                    \"联系专家进行咨询\"\n                ]\n            };\n            setMessages((prev)=>{\n                const newMessages = [\n                    ...prev,\n                    aiResponse\n                ];\n                // 自动保存对话\n                setTimeout(()=>autoSaveConversation(newMessages), 1000);\n                return newMessages;\n            });\n            finishLoading();\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已基于您的需求生成专业方案！\");\n        } catch (err) {\n            console.error(\"生成建议失败:\", err);\n            // 更新用户消息状态为错误\n            setMessages((prev)=>prev.map((msg)=>msg.id === userMessage.id ? {\n                        ...msg,\n                        status: \"error\"\n                    } : msg));\n            cancelLoading();\n        // 错误已经由errorHandler处理\n        }\n    };\n    // 生成需求总结 - 更新以匹配新的字段结构\n    const generateRequirementSummary = (reqs)=>{\n        const sections = [];\n        sections.push(\"📋 **项目需求总结**\\n\");\n        // 基础分类信息\n        if (reqs.speciesType) sections.push(\"\\uD83E\\uDDEC **物种类型**: \".concat(reqs.speciesType));\n        if (reqs.experimentType) sections.push(\"\\uD83D\\uDD2C **实验类型**: \".concat(reqs.experimentType));\n        if (reqs.researchGoal) sections.push(\"\\uD83C\\uDFAF **研究目标**: \".concat(reqs.researchGoal));\n        // 样本信息\n        if (reqs.sampleType || reqs.sampleCount || reqs.sampleCondition || reqs.cellCount) {\n            sections.push(\"\\n🧪 **样本信息**\");\n            if (reqs.sampleType) sections.push(\"• 样本类型: \".concat(reqs.sampleType));\n            if (reqs.sampleCount) sections.push(\"• 样本数目: \".concat(reqs.sampleCount));\n            if (reqs.sampleCondition) sections.push(\"• 样本状态: \".concat(reqs.sampleCondition));\n            if (reqs.sampleProcessing) sections.push(\"• 处理方式: \".concat(reqs.sampleProcessing));\n            if (reqs.cellCount) sections.push(\"• 细胞数量: \".concat(reqs.cellCount));\n            if (reqs.cellViability) sections.push(\"• 细胞活力: \".concat(reqs.cellViability));\n        }\n        // 项目规划\n        if (reqs.budget || reqs.timeline || reqs.urgencyLevel) {\n            sections.push(\"\\n📅 **项目规划**\");\n            if (reqs.budget) sections.push(\"• 预算范围: \".concat(reqs.budget));\n            if (reqs.timeline) sections.push(\"• 项目周期: \".concat(reqs.timeline));\n            if (reqs.urgencyLevel) sections.push(\"• 紧急程度: \".concat(reqs.urgencyLevel));\n        }\n        // 技术细节\n        if (reqs.sequencingDepth || reqs.analysisType || reqs.dataAnalysisNeeds || reqs.needsCellSorting || reqs.specialRequirements) {\n            sections.push(\"\\n⚙️ **技术细节**\");\n            if (reqs.sequencingDepth) sections.push(\"• 测序深度: \".concat(reqs.sequencingDepth));\n            if (reqs.analysisType) sections.push(\"• 分析类型: \".concat(reqs.analysisType));\n            if (reqs.dataAnalysisNeeds) sections.push(\"• 数据分析需求: \".concat(reqs.dataAnalysisNeeds));\n            if (reqs.needsCellSorting) sections.push(\"• 细胞分选: \".concat(reqs.needsCellSorting));\n            if (reqs.specialRequirements) sections.push(\"• 特殊要求: \".concat(reqs.specialRequirements));\n        }\n        sections.push(\"\\n请为我生成详细的技术方案建议。\");\n        return sections.join(\"\\n\");\n    };\n    // 格式化方案响应，包含文献搜索结果和个性化信息\n    const formatSolutionResponse = (solutionResponse, literatureResults)=>{\n        var _literatureResults_literature_results;\n        if (!solutionResponse) return \"抱歉，方案生成失败。\";\n        const { solution_id, client_requirements, recommended_solution, cost_analysis, risk_assessment, timeline, deliverables, next_steps, contact_info, intelligent_features, intent_analysis, research_domain, personalization_level, ai_analysis_features, fallback_notice } = solutionResponse;\n        const sections = [];\n        // 检测是否为个性化推荐或AI分析结果\n        const isPersonalized = personalization_level === \"high\" || intent_analysis;\n        const isIntelligentRecommendation = (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('intelligent')) || intelligent_features;\n        const isAiAnalyzed = (ai_analysis_features === null || ai_analysis_features === void 0 ? void 0 : ai_analysis_features.intent_analyzed) || (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('ai_analyzed'));\n        const isFallback = fallback_notice || (solution_id === null || solution_id === void 0 ? void 0 : solution_id.includes('fallback'));\n        // 根据个性化程度和研究领域定制标题\n        if (isFallback) {\n            sections.push(\"⚠️ **CellForge AI 基础方案**\");\n            sections.push(\"⏰ 系统正在升级，专家人工分析中...\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加回退通知\n            if (fallback_notice) {\n                sections.push(\"📢 **重要提示**\");\n                sections.push(\"• \".concat(fallback_notice.message));\n                if (fallback_notice.expert_contact) {\n                    sections.push(\"• 专家团队将在2小时内与您联系\");\n                }\n                sections.push(\"• 原因: \".concat(fallback_notice.reason));\n                sections.push(\"\");\n            }\n        } else if (isAiAnalyzed && ai_analysis_features) {\n            var _ai_analysis_features_analysis_sources;\n            sections.push(\"🧠 **CellForge AI 智能意图分析方案**\");\n            sections.push(\"✨ 基于深度用户意图分析的个性化推荐\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加AI分析摘要\n            sections.push(\"🔍 **AI意图分析结果**\");\n            sections.push(\"• 分析置信度：\".concat(Math.round((ai_analysis_features.confidence_score || 0.85) * 100), \"%\"));\n            if (((_ai_analysis_features_analysis_sources = ai_analysis_features.analysis_sources) === null || _ai_analysis_features_analysis_sources === void 0 ? void 0 : _ai_analysis_features_analysis_sources.length) > 0) {\n                sections.push(\"• 分析来源：\".concat(ai_analysis_features.analysis_sources.join(\", \")));\n            }\n            sections.push(\"• 个性化程度：高度定制\");\n            sections.push(\"\");\n        } else if (isPersonalized && research_domain) {\n            sections.push(\"\\uD83C\\uDFAF **CellForge AI \".concat(research_domain, \"专业方案**\"));\n            sections.push(\"✨ 基于意图分析的个性化推荐\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n            // 添加意图分析摘要\n            if (intent_analysis) {\n                sections.push(\"🧠 **用户意图分析**\");\n                sections.push(\"• 研究领域：\".concat(intent_analysis.research_domain || '通用研究'));\n                sections.push(\"• 预算类别：\".concat(intent_analysis.budget_category || '标准型'));\n                sections.push(\"• 紧急程度：\".concat(intent_analysis.urgency_level || '常规'));\n                sections.push(\"• 技术偏好：\".concat(intent_analysis.technical_preference || '10x Genomics'));\n                sections.push(\"• 置信度：\".concat(Math.round((intent_analysis.confidence_score || 0.85) * 100), \"%\"));\n                sections.push(\"\");\n            }\n        } else if (isIntelligentRecommendation) {\n            sections.push(\"🎯 **CellForge AI 智能推荐方案**\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n        } else {\n            sections.push(\"🎯 **CellForge AI 专业方案**\");\n            sections.push(\"方案编号: \".concat(solution_id));\n            sections.push(\"\");\n        }\n        // 推荐方案 - 支持新格式和旧格式\n        if (recommended_solution === null || recommended_solution === void 0 ? void 0 : recommended_solution.platform) {\n            sections.push(\"🔬 **推荐技术方案**\");\n            // 新格式（智能推荐）\n            if (typeof recommended_solution.platform === 'string') {\n                sections.push(\"**平台**: \".concat(recommended_solution.platform));\n                sections.push(\"**理由**: \".concat(recommended_solution.reasoning));\n                if (recommended_solution.specifications) {\n                    sections.push(\"**规格**: \".concat(JSON.stringify(recommended_solution.specifications).replace(/[{}\",]/g, ' ').replace(/:/g, ': ')));\n                }\n            } else {\n                sections.push(\"**平台**: \".concat(recommended_solution.platform.primary));\n                sections.push(\"**理由**: \".concat(recommended_solution.platform.reason));\n                sections.push(\"**规格**: \".concat(recommended_solution.platform.specifications));\n                sections.push(\"**预期细胞数**: \".concat(recommended_solution.platform.expected_cells));\n            }\n            sections.push(\"\");\n        }\n        // 成本分析 - 支持新格式和旧格式\n        if (cost_analysis) {\n            sections.push(\"💰 **成本分析**\");\n            // 新格式（智能推荐）\n            if (cost_analysis.total_cost) {\n                sections.push(\"**总费用**: \\xa5\".concat((cost_analysis.total_cost / 10000).toFixed(1), \"万\"));\n                sections.push(\"**费用明细**:\");\n                if (cost_analysis.sample_preparation) {\n                    sections.push(\"• 样本制备: \\xa5\".concat((cost_analysis.sample_preparation.cost / 10000).toFixed(1), \"万\"));\n                }\n                if (cost_analysis.sequencing) {\n                    sections.push(\"• 测序费用: \\xa5\".concat((cost_analysis.sequencing.cost / 10000).toFixed(1), \"万\"));\n                }\n                if (cost_analysis.data_analysis) {\n                    sections.push(\"• 数据分析: \\xa5\".concat((cost_analysis.data_analysis.cost / 10000).toFixed(1), \"万\"));\n                }\n                if (cost_analysis.breakdown) {\n                    sections.push(\"• \".concat(cost_analysis.breakdown));\n                }\n            } else if (cost_analysis.total_cost_range) {\n                sections.push(\"**总费用**: \".concat(cost_analysis.total_cost_range));\n                if (cost_analysis.cost_breakdown) {\n                    sections.push(\"**费用明细**:\");\n                    Object.entries(cost_analysis.cost_breakdown).forEach((param)=>{\n                        let [key, value] = param;\n                        sections.push(\"• \".concat(key, \": \").concat(value));\n                    });\n                }\n            }\n            sections.push(\"\");\n        }\n        // 项目时间规划 - 支持新格式和旧格式\n        if (timeline) {\n            sections.push(\"⏰ **项目时间规划**\");\n            // 新格式（智能推荐）\n            if (timeline.total) {\n                sections.push(\"**总周期**: \".concat(timeline.total));\n                sections.push(\"**阶段安排**:\");\n                if (timeline.sample_prep) sections.push(\"• 样本准备: \".concat(timeline.sample_prep));\n                if (timeline.sequencing) sections.push(\"• 测序分析: \".concat(timeline.sequencing));\n                if (timeline.analysis) sections.push(\"• 数据分析: \".concat(timeline.analysis));\n            } else if (timeline.total_duration) {\n                sections.push(\"**总周期**: \".concat(timeline.total_duration));\n                if (timeline.phases) {\n                    sections.push(\"**阶段安排**:\");\n                    Object.entries(timeline.phases).forEach((param)=>{\n                        let [phase, desc] = param;\n                        sections.push(\"• \".concat(phase, \": \").concat(desc));\n                    });\n                }\n            }\n            sections.push(\"\");\n        }\n        // 风险评估 - 支持新格式和旧格式\n        if (risk_assessment) {\n            var _risk_assessment_technical_risks, _risk_assessment_identified_risks;\n            sections.push(\"⚠️ **风险评估**\");\n            if (risk_assessment.success_probability) {\n                sections.push(\"**成功概率**: \".concat(risk_assessment.success_probability));\n            }\n            if (risk_assessment.overall_risk_level) {\n                sections.push(\"**整体风险等级**: \".concat(risk_assessment.overall_risk_level));\n            }\n            // 新格式技术风险\n            if (((_risk_assessment_technical_risks = risk_assessment.technical_risks) === null || _risk_assessment_technical_risks === void 0 ? void 0 : _risk_assessment_technical_risks.length) > 0) {\n                sections.push(\"**主要风险**:\");\n                risk_assessment.technical_risks.forEach((risk)=>{\n                    if (typeof risk === 'object') {\n                        sections.push(\"• \".concat(risk.risk, \" (\").concat(risk.probability, \")\"));\n                        sections.push(\"  影响: \".concat(risk.impact));\n                        sections.push(\"  缓解: \".concat(risk.mitigation));\n                    } else {\n                        sections.push(\"• \".concat(risk));\n                    }\n                });\n            } else if (((_risk_assessment_identified_risks = risk_assessment.identified_risks) === null || _risk_assessment_identified_risks === void 0 ? void 0 : _risk_assessment_identified_risks.length) > 0) {\n                sections.push(\"**主要风险**:\");\n                risk_assessment.identified_risks.forEach((risk)=>{\n                    sections.push(\"• \".concat(risk));\n                });\n            }\n            sections.push(\"\");\n        }\n        // 交付物\n        if ((deliverables === null || deliverables === void 0 ? void 0 : deliverables.length) > 0) {\n            sections.push(\"📦 **交付物清单**\");\n            deliverables.forEach((item)=>{\n                sections.push(\"• \".concat(item));\n            });\n            sections.push(\"\");\n        }\n        // 下一步行动\n        if ((next_steps === null || next_steps === void 0 ? void 0 : next_steps.length) > 0) {\n            sections.push(\"🚀 **下一步行动**\");\n            next_steps.forEach((step, index)=>{\n                sections.push(\"\".concat(index + 1, \". \").concat(step));\n            });\n            sections.push(\"\");\n        }\n        // 添加智能推荐特性（如果是智能推荐）\n        if (intelligent_features) {\n            var _intelligent_features_hot_papers, _intelligent_features_literature_recommendations_combined_results, _intelligent_features_literature_recommendations, _intelligent_features_smart_insights, _intelligent_features_collaboration_opportunities;\n            // 智能文献推荐 - 修复数据结构访问\n            if (((_intelligent_features_hot_papers = intelligent_features.hot_papers) === null || _intelligent_features_hot_papers === void 0 ? void 0 : _intelligent_features_hot_papers.length) > 0) {\n                sections.push(\"🧬 **智能文献推荐报告**\");\n                sections.push(\"\");\n                sections.push(\"🔥 **热点文献发现**:\");\n                intelligent_features.hot_papers.forEach((paper, index)=>{\n                    const pubmedLink = paper.doi ? \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.doi), \"[DOI]\") : \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.title));\n                    const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(paper.title));\n                    sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title, \"**\"));\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal, \" \").concat(paper.impact_factor ? \"(IF: \".concat(paper.impact_factor, \")\") : ''));\n                    sections.push(\"\\uD83D\\uDCA1 \".concat(paper.reason));\n                    sections.push(\"\\uD83D\\uDD17 [PubMed](\".concat(pubmedLink, \") | [Google Scholar](\").concat(scholarLink, \")\"));\n                    sections.push(\"\");\n                });\n            }\n            // 智能关键词扩展 - 修复数据结构访问并添加搜索链接\n            if (intelligent_features.expanded_keywords) {\n                var _keywords_semantic_expansion, _keywords_trending_terms;\n                const keywords = intelligent_features.expanded_keywords;\n                if (((_keywords_semantic_expansion = keywords.semantic_expansion) === null || _keywords_semantic_expansion === void 0 ? void 0 : _keywords_semantic_expansion.length) > 0 || ((_keywords_trending_terms = keywords.trending_terms) === null || _keywords_trending_terms === void 0 ? void 0 : _keywords_trending_terms.length) > 0) {\n                    var _keywords_semantic_expansion1, _keywords_trending_terms1, _keywords_molecular_targets;\n                    sections.push(\"🔍 **智能关键词扩展与搜索链接**:\");\n                    sections.push(\"\");\n                    if (((_keywords_semantic_expansion1 = keywords.semantic_expansion) === null || _keywords_semantic_expansion1 === void 0 ? void 0 : _keywords_semantic_expansion1.length) > 0) {\n                        sections.push(\"**语义扩展关键词**:\");\n                        keywords.semantic_expansion.slice(0, 5).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \") | [Scholar](\").concat(scholarLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                    if (((_keywords_trending_terms1 = keywords.trending_terms) === null || _keywords_trending_terms1 === void 0 ? void 0 : _keywords_trending_terms1.length) > 0) {\n                        sections.push(\"**热点趋势关键词**:\");\n                        keywords.trending_terms.slice(0, 5).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            const scholarLink = \"https://scholar.google.com/scholar?q=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \") | [Scholar](\").concat(scholarLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                    if (((_keywords_molecular_targets = keywords.molecular_targets) === null || _keywords_molecular_targets === void 0 ? void 0 : _keywords_molecular_targets.length) > 0) {\n                        sections.push(\"**分子靶点关键词**:\");\n                        keywords.molecular_targets.slice(0, 3).forEach((keyword)=>{\n                            const pubmedLink = \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(keyword));\n                            sections.push(\"• \".concat(keyword, \" - [PubMed](\").concat(pubmedLink, \")\"));\n                        });\n                        sections.push(\"\");\n                    }\n                }\n            }\n            // 外部文献搜索结果\n            if (((_intelligent_features_literature_recommendations = intelligent_features.literature_recommendations) === null || _intelligent_features_literature_recommendations === void 0 ? void 0 : (_intelligent_features_literature_recommendations_combined_results = _intelligent_features_literature_recommendations.combined_results) === null || _intelligent_features_literature_recommendations_combined_results === void 0 ? void 0 : _intelligent_features_literature_recommendations_combined_results.length) > 0) {\n                sections.push(\"📚 **相关文献搜索结果**:\");\n                sections.push(\"\");\n                intelligent_features.literature_recommendations.combined_results.slice(0, 3).forEach((paper, index)=>{\n                    var _paper_authors;\n                    const pubmedLink = paper.pubmed_id ? \"https://pubmed.ncbi.nlm.nih.gov/\".concat(paper.pubmed_id, \"/\") : paper.doi ? \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.doi), \"[DOI]\") : \"https://pubmed.ncbi.nlm.nih.gov/?term=\".concat(encodeURIComponent(paper.title));\n                    sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title, \"**\"));\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal, \" \").concat(paper.publication_year ? \"(\".concat(paper.publication_year, \")\") : ''));\n                    if (((_paper_authors = paper.authors) === null || _paper_authors === void 0 ? void 0 : _paper_authors.length) > 0) {\n                        sections.push(\"\\uD83D\\uDC65 \".concat(paper.authors.slice(0, 3).join(', ')).concat(paper.authors.length > 3 ? ' et al.' : ''));\n                    }\n                    if (paper.abstract) {\n                        sections.push(\"\\uD83D\\uDCDD \".concat(paper.abstract.substring(0, 200), \"...\"));\n                    }\n                    sections.push(\"\\uD83D\\uDD17 [查看原文](\".concat(pubmedLink, \") | 来源: \").concat(paper.source || 'Unknown'));\n                    sections.push(\"\");\n                });\n            }\n            // AI智能洞察\n            if (((_intelligent_features_smart_insights = intelligent_features.smart_insights) === null || _intelligent_features_smart_insights === void 0 ? void 0 : _intelligent_features_smart_insights.length) > 0) {\n                sections.push(\"💡 **AI智能洞察**\");\n                intelligent_features.smart_insights.forEach((insight)=>{\n                    sections.push(\"• \".concat(insight));\n                });\n                sections.push(\"\");\n            }\n            // 专家合作机会推荐\n            if (((_intelligent_features_collaboration_opportunities = intelligent_features.collaboration_opportunities) === null || _intelligent_features_collaboration_opportunities === void 0 ? void 0 : _intelligent_features_collaboration_opportunities.length) > 0) {\n                sections.push(\"🤝 **专家合作机会推荐**\");\n                intelligent_features.collaboration_opportunities.forEach((collab)=>{\n                    sections.push(\"• **\".concat(collab.institution, \"** - \").concat(collab.expert));\n                    sections.push(\"  \".concat(collab.collaboration_type));\n                });\n                sections.push(\"\");\n            }\n            // 智能文献检索建议\n            if (intelligent_features.search_suggestions) {\n                sections.push(\"🔍 **智能文献检索建议**\");\n                sections.push(\"\");\n                sections.push(\"📌 **推荐检索关键词**:\");\n                intelligent_features.search_suggestions.primaryKeywords.forEach((keyword, index)=>{\n                    sections.push(\"\".concat(index + 1, \". **\").concat(keyword, \"**\"));\n                    // 为每个主要关键词添加搜索链接\n                    const links = intelligent_features.search_suggestions.searchLinks[keyword];\n                    if (links && links.length > 0) {\n                        const linkTexts = links.map((link)=>\"[\".concat(link.emoji, \" \").concat(link.name, \"](\").concat(link.url, \")\")).join(' • ');\n                        sections.push(\"   \".concat(linkTexts));\n                    }\n                    sections.push(\"\");\n                });\n                if (intelligent_features.search_suggestions.secondaryKeywords.length > 0) {\n                    sections.push(\"🔖 **扩展检索关键词**:\");\n                    intelligent_features.search_suggestions.secondaryKeywords.slice(0, 4).forEach((keyword)=>{\n                        sections.push(\"• \".concat(keyword));\n                    });\n                    sections.push(\"\");\n                }\n                sections.push(\"💡 **检索建议**:\");\n                sections.push(\"• 建议先用主要关键词在PubMed中检索最新文献\");\n                sections.push(\"• 使用Google Scholar查找引用次数高的经典文献\");\n                sections.push(\"• Semantic Scholar可发现相关的综述文章\");\n                sections.push(\"• 组合关键词可获得更精准的搜索结果\");\n                sections.push(\"\");\n            }\n        }\n        // 添加文献搜索结果（如果启用了文献搜索）\n        if (literatureResults && ((_literatureResults_literature_results = literatureResults.literature_results) === null || _literatureResults_literature_results === void 0 ? void 0 : _literatureResults_literature_results.length) > 0) {\n            sections.push(\"\");\n            sections.push(\"📚 **相关文献支撑**\");\n            sections.push(\"\");\n            // 显示搜索到的文献（最多显示3篇）\n            const papersToShow = literatureResults.literature_results.slice(0, 3);\n            papersToShow.forEach((paper, index)=>{\n                sections.push(\"**[\".concat(index + 1, \"] \").concat(paper.title || '文献标题', \"**\"));\n                if (paper.authors && paper.authors.length > 0) {\n                    const authorList = paper.authors.slice(0, 3).join(\", \");\n                    const moreAuthors = paper.authors.length > 3 ? \" 等\" : \"\";\n                    sections.push(\"*\".concat(authorList).concat(moreAuthors, \"*\"));\n                }\n                if (paper.journal) {\n                    sections.push(\"\\uD83D\\uDCD6 \".concat(paper.journal).concat(paper.publication_year ? \" (\".concat(paper.publication_year, \")\") : ''));\n                }\n                if (paper.key_findings) {\n                    sections.push(\"\\uD83D\\uDCA1 **核心发现**: \".concat(paper.key_findings));\n                }\n                if (paper.methodology_summary) {\n                    sections.push(\"\\uD83D\\uDD2C **方法**: \".concat(paper.methodology_summary));\n                }\n                sections.push(\"\");\n            });\n            // 显示搜索统计信息\n            if (literatureResults.total_papers > 3) {\n                sections.push(\"\\uD83D\\uDCCA **搜索统计**: 共找到 \".concat(literatureResults.total_papers, \" 篇相关文献，已显示最相关的 \").concat(papersToShow.length, \" 篇\"));\n                sections.push(\"\");\n            }\n            // 显示数据来源\n            if (literatureResults.sources && literatureResults.sources.length > 0) {\n                sections.push(\"\\uD83D\\uDD0D **数据来源**: \".concat(literatureResults.sources.join(\", \")));\n                sections.push(\"\");\n            }\n        }\n        // 处理综合方案框架（按设计文档要求）\n        const { comprehensive_framework, framework_features } = solutionResponse;\n        if (comprehensive_framework && (framework_features === null || framework_features === void 0 ? void 0 : framework_features.comprehensive_framework)) {\n            sections.push(\"\");\n            sections.push(\"🎯 **CellForge AI 综合方案框架**\");\n            sections.push(\"\");\n            // 添加综合方案框架的JSON数据供前端组件解析\n            sections.push(\"```json\");\n            sections.push(JSON.stringify({\n                type: \"comprehensive_framework\",\n                data: comprehensive_framework\n            }, null, 2));\n            sections.push(\"```\");\n            sections.push(\"\");\n            sections.push(\"💡 **框架功能说明**:\");\n            sections.push(\"• **方案概览**: 研究类型、成本估算、平台推荐和风险等级\");\n            sections.push(\"• **研究意图分析**: 精准搜索链接和研究焦点展示\");\n            sections.push(\"• **关键要素分析**: 成功关键因素和风险评估\");\n            sections.push(\"• **文献推荐**: 核心文献和最新研究推荐\");\n            sections.push(\"• **平台对比**: 技术平台推荐排序和对比分析\");\n            sections.push(\"• **实施规划**: 项目时间轴和里程碑规划\");\n            sections.push(\"\");\n        }\n        // 处理综合解决方案（5个核心功能）- 作为降级方案\n        const { comprehensive_solution, enhanced_features } = solutionResponse;\n        if (comprehensive_solution && (enhanced_features === null || enhanced_features === void 0 ? void 0 : enhanced_features.five_core_functions)) {\n            sections.push(\"\");\n            sections.push(\"🎯 **CellForge AI 5大核心功能综合分析**\");\n            sections.push(\"\");\n            // 添加综合解决方案的JSON数据供前端组件解析\n            sections.push(\"```json\");\n            sections.push(JSON.stringify({\n                type: \"comprehensive_solution\",\n                data: comprehensive_solution\n            }, null, 2));\n            sections.push(\"```\");\n            sections.push(\"\");\n            sections.push(\"💡 **功能说明**:\");\n            sections.push(\"• **个性化方案**: 基于您的需求定制的专业技术方案\");\n            sections.push(\"• **文献推荐**: 领域相关的高质量学术文献\");\n            sections.push(\"• **搜索关键词**: 优化的学术检索关键词组合\");\n            sections.push(\"• **痛点分析**: 该领域常见技术挑战及解决方案\");\n            sections.push(\"• **风险评估**: 项目实施风险评估及缓解策略\");\n            sections.push(\"\");\n        }\n        sections.push(\"---\");\n        if (isFallback) {\n            sections.push(\"🧬 **CellForge AI** - 您的单细胞测序专业伙伴\");\n            sections.push(\"\");\n            sections.push(\"**服务状态**: 临时方案 • **专家支持**: 人工分析中 • **预计响应**: 2小时内\");\n        } else if (isAiAnalyzed) {\n            sections.push(\"🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴\");\n            sections.push(\"\");\n            const confidence = (ai_analysis_features === null || ai_analysis_features === void 0 ? void 0 : ai_analysis_features.confidence_score) ? Math.round(ai_analysis_features.confidence_score * 100) : 95;\n            sections.push(\"**AI分析置信度**: \".concat(confidence, \"% • **来源**: AI意图分析引擎, 专业知识库, 个性化算法\"));\n        } else if (isIntelligentRecommendation) {\n            sections.push(\"🧬 **CellForge AI** - 您的智能单细胞测序专业伙伴\");\n            sections.push(\"\");\n            sections.push(\"**置信度**: 95% • **来源**: AI智能推荐引擎, 专业知识库, 热点文献发现\");\n        } else {\n            sections.push(\"🧬 **CellForge AI** - 您的单细胞测序专业伙伴\");\n        }\n        return sections.join(\"\\n\");\n    };\n    // 基于需求生成AI建议 - 使用增强的加载状态\n    const generateRequirementBasedSuggestion = async (reqs)=>{\n        const suggestionMessage = \"基于您填写的需求信息：\\n- 研究目标：\".concat(reqs.researchGoal, \"\\n- 样本类型：\").concat(reqs.sampleType, \"\\n- 细胞数量：\").concat(reqs.cellCount, \"\\n- 预算范围：\").concat(reqs.budget, \"\\n- 项目周期：\").concat(reqs.timeline, \"\\n\\n我来为您生成专业的技术方案建议。\");\n        try {\n            // 使用分阶段加载\n            let apiResponse;\n            await _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.errorHandler.handleStagedOperation([\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.ANALYZING,\n                    operation: async ()=>{\n                        await new Promise((resolve)=>setTimeout(resolve, 500));\n                        return {\n                            step: \"analyzed\"\n                        };\n                    }\n                },\n                {\n                    stage: _lib_enhanced_error_handler__WEBPACK_IMPORTED_MODULE_8__.LoadingStage.GENERATING_RESPONSE,\n                    operation: async ()=>{\n                        apiResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.conversationApi.sendMessage({\n                            message: suggestionMessage,\n                            conversation_type: \"requirement_based\",\n                            context: {\n                                user_profile: {\n                                    id: user === null || user === void 0 ? void 0 : user.id,\n                                    organization: (user === null || user === void 0 ? void 0 : user.organization) || \"\",\n                                    role: (user === null || user === void 0 ? void 0 : user.role) || \"\"\n                                },\n                                requirements: reqs\n                            }\n                        });\n                        return apiResponse;\n                    }\n                }\n            ], (stage, progress)=>{\n                if (!isLoading) {\n                    startLoading(stage);\n                } else {\n                    updateStage(stage, progress);\n                }\n            }, {\n                maxRetries: 1,\n                retryDelay: 3000,\n                userFriendlyMessage: \"生成建议失败，请稍后重试\",\n                showToast: false\n            });\n            // apiResponse已经在GENERATING_RESPONSE阶段中设置\n            const aiSuggestion = {\n                id: Date.now().toString(),\n                type: \"ai\",\n                content: apiResponse.message,\n                timestamp: new Date(),\n                status: \"read\",\n                confidence: apiResponse.confidence,\n                sources: apiResponse.sources,\n                suggestions: apiResponse.suggestions\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiSuggestion\n                ]);\n            finishLoading();\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已基于您的需求生成专业建议！\");\n        } catch (err) {\n            console.error(\"生成建议失败:\", err);\n            cancelLoading();\n        // 错误已经由errorHandler处理\n        }\n    };\n    // 获取缺失的关键需求字段 - 更新以匹配新的必需字段\n    const getMissingRequirements = ()=>{\n        const requiredFields = [\n            {\n                key: \"speciesType\",\n                label: \"物种类型\",\n                question: \"您研究的是什么物种？\"\n            },\n            {\n                key: \"experimentType\",\n                label: \"实验类型\",\n                question: \"您需要什么类型的单细胞实验？\"\n            },\n            {\n                key: \"researchGoal\",\n                label: \"研究目标\",\n                question: \"您的研究目标是什么？\"\n            },\n            {\n                key: \"sampleType\",\n                label: \"样本类型\",\n                question: \"您使用什么类型的样本？\"\n            },\n            {\n                key: \"budget\",\n                label: \"预算范围\",\n                question: \"您的项目预算大概是多少？\"\n            }\n        ];\n        if (!requirements) return requiredFields;\n        return requiredFields.filter((field)=>!requirements[field.key]);\n    };\n    // 获取智能建议（合并AI建议和快速回复）\n    const getSmartSuggestions = ()=>{\n        // 优先显示AI回复中的建议\n        const lastMessage = messages[messages.length - 1];\n        if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.type) === \"ai\" && lastMessage.suggestions && lastMessage.suggestions.length > 0) {\n            return {\n                type: \"ai_suggestions\",\n                title: \"AI智能建议\",\n                icon: \"✨\",\n                items: lastMessage.suggestions.slice(0, 4).map((suggestion)=>({\n                        text: suggestion,\n                        icon: \"💡\"\n                    }))\n            };\n        }\n        // 检查是否有缺失的关键需求信息\n        const missingReqs = getMissingRequirements();\n        if (missingReqs.length > 0 && (!requirements || requirements.completeness < 60)) {\n            return {\n                type: \"requirement_framework\",\n                title: \"完善需求信息\",\n                icon: \"📋\",\n                subtitle: \"还需要填写 \".concat(missingReqs.length, \" 项关键信息，建议先完善后再提交\"),\n                items: missingReqs.slice(0, 3).map((req)=>({\n                        text: req.question,\n                        icon: \"❓\",\n                        action: \"fill_requirement\",\n                        field: req.key\n                    }))\n            };\n        }\n        // 否则显示基于需求完成度的快速开始建议\n        if (!requirements || requirements.completeness < 20) {\n            return {\n                type: \"quick_start\",\n                title: \"快速开始\",\n                icon: \"💬\",\n                items: [\n                    {\n                        text: \"我需要单细胞RNA测序方案\",\n                        icon: \"🧬\"\n                    },\n                    {\n                        text: \"请推荐技术平台\",\n                        icon: \"⚡\"\n                    },\n                    {\n                        text: \"分析项目成本\",\n                        icon: \"💰\"\n                    },\n                    {\n                        text: \"查看成功案例\",\n                        icon: \"📊\"\n                    }\n                ]\n            };\n        } else if (requirements.completeness < 80) {\n            return {\n                type: \"continue_conversation\",\n                title: \"继续对话\",\n                icon: \"🔄\",\n                items: [\n                    {\n                        text: \"继续完善需求信息\",\n                        icon: \"📝\"\n                    },\n                    {\n                        text: \"基于当前信息给建议\",\n                        icon: \"💡\"\n                    },\n                    {\n                        text: \"了解技术细节\",\n                        icon: \"🔬\"\n                    },\n                    {\n                        text: \"预估项目周期\",\n                        icon: \"⏱️\"\n                    }\n                ]\n            };\n        } else {\n            return {\n                type: \"advanced_actions\",\n                title: \"高级功能\",\n                icon: \"🚀\",\n                items: [\n                    {\n                        text: \"生成完整技术方案\",\n                        icon: \"📋\"\n                    },\n                    {\n                        text: \"优化成本配置\",\n                        icon: \"💰\"\n                    },\n                    {\n                        text: \"风险评估分析\",\n                        icon: \"⚠️\"\n                    },\n                    {\n                        text: \"联系技术专家\",\n                        icon: \"👨‍🔬\"\n                    }\n                ]\n            };\n        }\n    };\n    // 处理建议点击\n    const handleSuggestionClick = (item)=>{\n        if (item.action === \"fill_requirement\") {\n            // 如果是需求填写建议，显示右侧面板并聚焦到对应字段\n            setRightPanelCollapsed(false);\n            // 可以添加滚动到对应字段的逻辑\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.info(\"请在右侧面板填写：\".concat(item.text));\n        } else {\n            // 普通建议直接设置到输入框\n            setInputValue(item.text);\n        }\n    };\n    // 加载历史对话\n    const handleLoadConversation = (session)=>{\n        setMessages(session.messages);\n        setCurrentSessionId(session.id);\n        setShowHistory(false);\n        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已加载对话: \".concat(session.title));\n    };\n    // 保存当前对话\n    const handleSaveCurrentConversation = ()=>{\n        // 这个函数会在ConversationHistory组件中处理\n        setShowHistory(false);\n    };\n    // 开始新对话\n    const handleNewConversation = ()=>{\n        setMessages([]);\n        setCurrentSessionId(null);\n        setRequirements(null);\n        setError(\"\");\n        setShowHistory(false);\n        setRightPanelCollapsed(false) // 显示需求收集助手\n        ;\n        setResetTrigger((prev)=>prev + 1) // 触发需求收集助手重置\n        ;\n        sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"已开始新对话，请在右侧填写项目需求\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full bg-slate-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full transition-all duration-300 \".concat(rightPanelCollapsed ? 'flex-1' : 'flex-1'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-b border-slate-200 px-4 py-3 flex items-center justify-between flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1365,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"font-semibold text-slate-900\",\n                                                        children: \"CellForge AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-slate-500\",\n                                                        children: \"单细胞测序智能顾问\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1368,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1364,\n                                        columnNumber: 13\n                                    }, this),\n                                    requirements && requirements.completeness > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"bg-blue-100 text-blue-800\",\n                                        children: [\n                                            \"需求完成度 \",\n                                            requirements.completeness,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowHistory(!showHistory),\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1388,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"历史\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleNewConversation,\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1399,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"新对话\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 13\n                                    }, this),\n                                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setRightPanelCollapsed(!rightPanelCollapsed),\n                                        className: \"text-slate-600 hover:text-slate-900\",\n                                        children: rightPanelCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1412,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"显示助手\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"隐藏助手\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1404,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1380,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1362,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                            variant: \"destructive\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1430,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1431,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1429,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1428,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto p-4 space-y-6 bg-white\",\n                            children: [\n                                messages.map((message)=>{\n                                    var _user_name;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                        children: [\n                                            message.type === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1444,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\".concat(message.type === \"user\" ? \"max-w-lg ml-12\" : \"max-w-4xl mr-8\" // Give AI messages more space\n                                                ),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"\".concat(message.type === \"user\" ? \"px-4 py-3 rounded-2xl shadow-sm\" : \"px-6 py-4 rounded-2xl shadow-sm\" // More padding for AI messages\n                                                        , \" \").concat(message.type === \"user\" ? message.status === \"error\" ? \"bg-red-500 text-white\" : \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white\" : \"bg-white border border-slate-200 text-slate-900\" // White background for AI\n                                                        ),\n                                                        children: [\n                                                            message.type === \"ai\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_formatted_message__WEBPACK_IMPORTED_MODULE_12__.FormattedMessage, {\n                                                                content: message.content,\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1467,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1469,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            message.type === \"ai\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-3 pt-2 border-t border-slate-200 space-y-1\",\n                                                                children: [\n                                                                    message.confidence !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1477,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-slate-600\",\n                                                                                children: [\n                                                                                    \"置信度: \",\n                                                                                    Math.round(message.confidence * 100),\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1478,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    message.sources && message.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-green-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1485,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-slate-600\",\n                                                                                children: [\n                                                                                    \"来源: \",\n                                                                                    message.sources.slice(0, 2).join(\", \"),\n                                                                                    message.sources.length > 2 && \"...\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                                lineNumber: 1486,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                        lineNumber: 1484,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1474,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-2 flex items-center \".concat(message.type === \"user\" ? \"justify-end text-slate-500\" : \"justify-start text-slate-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: message.timestamp.toLocaleTimeString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1499,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2\",\n                                                                children: [\n                                                                    message.status === \"sending\" && \"⏳\",\n                                                                    message.status === \"sent\" && \"✓\",\n                                                                    message.status === \"read\" && \"✓✓\",\n                                                                    message.status === \"error\" && \"❌\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                                lineNumber: 1501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1496,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1448,\n                                                columnNumber: 17\n                                            }, this),\n                                            message.type === \"user\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-slate-400 to-slate-500 rounded-full flex items-center justify-center ml-3 flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-sm font-medium\",\n                                                    children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0)) || \"U\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1513,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1512,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, message.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1441,\n                                        columnNumber: 15\n                                    }, this);\n                                }),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1524,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                            lineNumber: 1523,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 max-w-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.EnhancedLoadingIndicator, {\n                                                isLoading: isLoading,\n                                                stage: currentStage,\n                                                progress: progress,\n                                                estimatedTime: 30,\n                                                onCancel: cancelLoading,\n                                                onRetry: ()=>{\n                                                    cancelLoading();\n                                                    handleSendMessage();\n                                                },\n                                                showRetryAfter: 60,\n                                                allowCancel: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1527,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                            lineNumber: 1526,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1522,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1543,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1439,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1437,\n                        columnNumber: 9\n                    }, this),\n                    (()=>{\n                        const suggestions = getSmartSuggestions();\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-slate-200 p-4 flex-shrink-0 \".concat(suggestions.type === \"ai_suggestions\" ? \"bg-gradient-to-r from-blue-50 to-indigo-50\" : suggestions.type === \"requirement_framework\" ? \"bg-gradient-to-r from-amber-50 to-orange-50\" : \"bg-white\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-3\",\n                                        children: [\n                                            suggestions.type === \"ai_suggestions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1561,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: suggestions.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1563,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium \".concat(suggestions.type === \"ai_suggestions\" ? \"text-blue-800\" : suggestions.type === \"requirement_framework\" ? \"text-amber-800\" : \"text-slate-600\"),\n                                                children: suggestions.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1565,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 17\n                                    }, this),\n                                    suggestions.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-amber-700 mb-3 p-2 bg-amber-100 rounded-lg border border-amber-200\",\n                                        children: suggestions.subtitle\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1578,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: suggestions.items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleSuggestionClick(item),\n                                                className: \"text-xs \".concat(suggestions.type === \"ai_suggestions\" ? \"bg-white hover:bg-blue-50 border-blue-200 text-blue-700 hover:text-blue-800\" : suggestions.type === \"requirement_framework\" ? \"bg-white hover:bg-amber-50 border-amber-200 text-amber-700 hover:text-amber-800\" : \"bg-slate-50 hover:bg-slate-100 border-slate-200\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                        lineNumber: 1598,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.text\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1585,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1583,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1551,\n                            columnNumber: 13\n                        }, this);\n                    })(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-200 p-4 bg-white flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 bg-slate-50 rounded-xl p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"text-slate-500 hover:text-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1612,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1611,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"text-slate-500 hover:text-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1614,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    value: inputValue,\n                                    onChange: (e)=>setInputValue(e.target.value),\n                                    placeholder: \"输入您的问题，我来为您提供专业建议...\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && !e.shiftKey && handleSendMessage(),\n                                    className: \"flex-1 border-0 bg-white shadow-sm focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSendMessage,\n                                    disabled: !inputValue.trim() || isLoading,\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1624,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1610,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1609,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1358,\n                columnNumber: 7\n            }, this),\n            (!rightPanelCollapsed || showHistory) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-l border-slate-200 bg-white transition-all duration-300 flex flex-col h-full \".concat(isMobile ? 'absolute right-0 top-0 bottom-0 w-80 shadow-xl z-10' : 'w-80 flex-shrink-0'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-200 p-4 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: showHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1646,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-slate-900\",\n                                                    children: \"对话历史\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1647,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-5 w-5 text-indigo-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1651,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-slate-900\",\n                                                    children: \"需求收集助手\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1652,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1643,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            !showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowHistory(true),\n                                                className: \"text-slate-500 hover:text-slate-700 h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1666,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 19\n                                            }, this),\n                                            showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowHistory(false),\n                                                className: \"text-slate-500 hover:text-slate-700 h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1676,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1670,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>{\n                                                    setRightPanelCollapsed(true);\n                                                    setShowHistory(false);\n                                                },\n                                                className: \"text-slate-500 h-6 w-6 p-0\",\n                                                title: \"隐藏面板\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                    lineNumber: 1690,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                        lineNumber: 1657,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1642,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-slate-600 mt-1\",\n                                children: showHistory ? \"查看和管理您的对话历史\" : \"快速填写项目需求，获得更精准的AI建议\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1694,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1641,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 relative\",\n                        children: showHistory ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_conversation_history__WEBPACK_IMPORTED_MODULE_13__.ConversationHistory, {\n                                onLoadConversation: handleLoadConversation,\n                                currentMessages: messages,\n                                onSaveCurrentConversation: handleSaveCurrentConversation\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                lineNumber: 1706,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1705,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_literature_search_toggle__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    enabled: enableLiteratureSearch,\n                                    onToggle: setEnableLiteratureSearch,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1715,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_progressive_requirement_collector__WEBPACK_IMPORTED_MODULE_11__.ProgressiveRequirementCollector, {\n                                    onRequirementsChange: handleRequirementsChange,\n                                    onSubmitRequirements: handleRequirementsSubmit,\n                                    isCompact: true,\n                                    resetTrigger: resetTrigger\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                                    lineNumber: 1722,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                            lineNumber: 1713,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                        lineNumber: 1703,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1637,\n                columnNumber: 9\n            }, this),\n            isMobile && !rightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-5\",\n                onClick: ()=>setRightPanelCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1736,\n                columnNumber: 9\n            }, this),\n            isMobile && rightPanelCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                className: \"fixed bottom-4 right-4 rounded-full w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg z-10\",\n                onClick: ()=>setRightPanelCollapsed(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Brain_ChevronLeft_ChevronRight_ClipboardList_History_MessageSquare_Mic_Paperclip_RotateCcw_Send_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                    lineNumber: 1748,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n                lineNumber: 1744,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Dev\\\\CellForge AI\\\\frontend\\\\components\\\\conversation-interface.tsx\",\n        lineNumber: 1356,\n        columnNumber: 5\n    }, this);\n}\n_s(ConversationInterface, \"YqxGvOJHMwSGRzXwQIpKNTlg44s=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _enhanced_loading_indicator__WEBPACK_IMPORTED_MODULE_9__.useStageLoading\n    ];\n});\n_c = ConversationInterface;\nvar _c;\n$RefreshReg$(_c, \"ConversationInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/conversation-interface.tsx\n"));

/***/ })

});